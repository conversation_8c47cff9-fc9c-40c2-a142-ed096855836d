import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getRecipes, searchRecipes, filterRecipes } from '../services/api';
import { searchMealsByName, filterByCategory, getCategories, getAreas, filterByArea, convertMealToRecipe } from '../services/mealdb';

const RecipeList = () => {
  const [recipes, setRecipes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    difficulty: '',
    minTime: '',
    maxTime: '',
    minServings: '',
    maxServings: '',
    category: '',
    area: ''
  });
  const [showFilters, setShowFilters] = useState(false);
  const [categories, setCategories] = useState([]);
  const [areas, setAreas] = useState([]);
  const [useExternalApi, setUseExternalApi] = useState(true);

  useEffect(() => {
    if (useExternalApi) {
      fetchExternalRecipes();
      fetchCategories();
      fetchAreas();
    } else {
      fetchRecipes();
    }
  }, [useExternalApi]);

  const fetchRecipes = async (params = {}) => {
    setLoading(true);
    try {
      const data = await getRecipes(params);
      setRecipes(data);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch recipes');
      setLoading(false);
    }
  };

  const fetchExternalRecipes = async (searchTerm = '') => {
    setLoading(true);
    try {
      let data;
      if (searchTerm) {
        data = await searchMealsByName(searchTerm);
      } else {
        // Default to pasta if no search term
        data = await searchMealsByName('pasta');
      }

      // Convert TheMealDB format to our app's format
      const formattedRecipes = data.map(meal => convertMealToRecipe(meal));
      setRecipes(formattedRecipes);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch recipes from external API');
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const data = await getCategories();
      setCategories(data);
    } catch (err) {
      console.error('Failed to fetch categories:', err);
    }
  };

  const fetchAreas = async () => {
    try {
      const data = await getAreas();
      setAreas(data);
    } catch (err) {
      console.error('Failed to fetch areas:', err);
    }
  };

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchTerm.trim()) {
      if (useExternalApi) {
        fetchExternalRecipes();
      } else {
        fetchRecipes(filters);
      }
      return;
    }

    setLoading(true);
    try {
      if (useExternalApi) {
        await fetchExternalRecipes(searchTerm);
      } else {
        const data = await searchRecipes(searchTerm);
        setRecipes(data);
      }
      setLoading(false);
    } catch (err) {
      setError('Failed to search recipes');
      setLoading(false);
    }
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const applyFilters = async () => {
    setLoading(true);
    try {
      if (useExternalApi) {
        // Handle external API filtering
        let filteredData = [];

        if (filters.category && filters.category !== '') {
          filteredData = await filterByCategory(filters.category);
        } else if (filters.area && filters.area !== '') {
          filteredData = await filterByArea(filters.area);
        } else if (searchTerm.trim()) {
          filteredData = await searchMealsByName(searchTerm);
        } else {
          filteredData = await searchMealsByName('pasta'); // Default search
        }

        // Convert to our format
        const formattedRecipes = filteredData.map(meal => convertMealToRecipe(meal));

        // Apply client-side filtering for other filters
        let finalRecipes = formattedRecipes;

        if (filters.difficulty && filters.difficulty !== '') {
          finalRecipes = finalRecipes.filter(recipe => recipe.difficulty === filters.difficulty);
        }

        if (filters.minTime && filters.minTime !== '') {
          finalRecipes = finalRecipes.filter(recipe => recipe.cookingTime >= parseInt(filters.minTime));
        }

        if (filters.maxTime && filters.maxTime !== '') {
          finalRecipes = finalRecipes.filter(recipe => recipe.cookingTime <= parseInt(filters.maxTime));
        }

        if (filters.minServings && filters.minServings !== '') {
          finalRecipes = finalRecipes.filter(recipe => recipe.servings >= parseInt(filters.minServings));
        }

        if (filters.maxServings && filters.maxServings !== '') {
          finalRecipes = finalRecipes.filter(recipe => recipe.servings <= parseInt(filters.maxServings));
        }

        setRecipes(finalRecipes);
      } else {
        // Handle local API filtering
        // Only include non-empty filters
        const activeFilters = Object.entries(filters)
          .filter(([key, value]) => value !== '' && key !== 'category' && key !== 'area')
          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

        // Add search term if it exists
        if (searchTerm.trim()) {
          activeFilters.search = searchTerm;
        }

        const data = await getRecipes(activeFilters);
        setRecipes(data);
      }

      setLoading(false);
    } catch (err) {
      setError('Failed to filter recipes');
      setLoading(false);
    }
  };

  const resetFilters = () => {
    setFilters({
      difficulty: '',
      minTime: '',
      maxTime: '',
      minServings: '',
      maxServings: '',
      category: '',
      area: ''
    });
    setSearchTerm('');
    if (useExternalApi) {
      fetchExternalRecipes();
    } else {
      fetchRecipes();
    }
  };

  const toggleApiSource = () => {
    setUseExternalApi(!useExternalApi);
  };

  if (loading) {
    return <div className="text-center mt-5"><div className="spinner-border"></div></div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (recipes.length === 0) {
    return (
      <div className="text-center mt-5">
        <h2>No recipes found</h2>
        <Link to="/add-recipe" className="btn btn-primary mt-3">Add Recipe</Link>
      </div>
    );
  }

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>All Recipes</h2>
        <div>
          <button
            className="btn btn-outline-primary me-2"
            onClick={toggleApiSource}
          >
            {useExternalApi ? 'Use Local API' : 'Use TheMealDB API'}
          </button>
          <button
            className="btn btn-outline-secondary"
            onClick={() => setShowFilters(!showFilters)}
          >
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
        </div>
      </div>

      <div className="mb-4">
        <form onSubmit={handleSearch} className="d-flex">
          <input
            type="text"
            className="form-control me-2"
            placeholder="Search recipes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button type="submit" className="btn btn-primary">
            Search
          </button>
        </form>
      </div>

      {showFilters && (
        <div className="card mb-4">
          <div className="card-body">
            <h5 className="card-title mb-3">Filter Recipes</h5>
            <div className="row g-3">
              <div className="col-md-3">
                <label className="form-label">Difficulty</label>
                <select
                  className="form-select"
                  name="difficulty"
                  value={filters.difficulty}
                  onChange={handleFilterChange}
                >
                  <option value="">All Difficulties</option>
                  <option value="Easy">Easy</option>
                  <option value="Medium">Medium</option>
                  <option value="Hard">Hard</option>
                </select>
              </div>

              {useExternalApi && (
                <>
                  <div className="col-md-3">
                    <label className="form-label">Category</label>
                    <select
                      className="form-select"
                      name="category"
                      value={filters.category}
                      onChange={handleFilterChange}
                    >
                      <option value="">All Categories</option>
                      {categories.map(category => (
                        <option key={category.idCategory} value={category.strCategory}>
                          {category.strCategory}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="col-md-3">
                    <label className="form-label">Cuisine</label>
                    <select
                      className="form-select"
                      name="area"
                      value={filters.area}
                      onChange={handleFilterChange}
                    >
                      <option value="">All Cuisines</option>
                      {areas.map(area => (
                        <option key={area.strArea} value={area.strArea}>
                          {area.strArea}
                        </option>
                      ))}
                    </select>
                  </div>
                </>
              )}

              <div className="col-md-3">
                <label className="form-label">Cooking Time (min)</label>
                <div className="d-flex">
                  <input
                    type="number"
                    className="form-control me-2"
                    placeholder="Min"
                    name="minTime"
                    value={filters.minTime}
                    onChange={handleFilterChange}
                  />
                  <input
                    type="number"
                    className="form-control"
                    placeholder="Max"
                    name="maxTime"
                    value={filters.maxTime}
                    onChange={handleFilterChange}
                  />
                </div>
              </div>

              <div className="col-md-3">
                <label className="form-label">Servings</label>
                <div className="d-flex">
                  <input
                    type="number"
                    className="form-control me-2"
                    placeholder="Min"
                    name="minServings"
                    value={filters.minServings}
                    onChange={handleFilterChange}
                  />
                  <input
                    type="number"
                    className="form-control"
                    placeholder="Max"
                    name="maxServings"
                    value={filters.maxServings}
                    onChange={handleFilterChange}
                  />
                </div>
              </div>
            </div>

            <div className="d-flex justify-content-end mt-3">
              <button
                type="button"
                className="btn btn-outline-secondary me-2"
                onClick={resetFilters}
              >
                Reset
              </button>
              <button
                type="button"
                className="btn btn-primary"
                onClick={applyFilters}
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
        {recipes.map((recipe) => (
          <div className="col" key={recipe._id}>
            <div className="card h-100 recipe-card">
              <div className="position-relative">
                <img
                  src={recipe.imageUrl || 'https://via.placeholder.com/300x200?text=No+Image'}
                  className="card-img-top recipe-image"
                  alt={recipe.title}
                />
                <span className={`badge difficulty-badge bg-${
                  recipe.difficulty === 'Easy' ? 'success' :
                  recipe.difficulty === 'Medium' ? 'warning' : 'danger'
                }`}>
                  {recipe.difficulty}
                </span>
              </div>
              <div className="card-body">
                <h5 className="card-title">{recipe.title}</h5>
                <p className="card-text">{recipe.description.substring(0, 100)}...</p>
                <div className="d-flex justify-content-between align-items-center">
                  <small className="text-muted">
                    {recipe.cookingTime} mins | {recipe.servings} servings
                  </small>
                  <Link to={`/recipes/${recipe._id}`} className="btn btn-sm btn-primary">
                    View Recipe
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecipeList;
