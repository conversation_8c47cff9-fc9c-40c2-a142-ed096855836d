/**
 * Utility function to handle API errors consistently
 * @param {Error} error - The error object from the API call
 * @param {string} defaultMessage - Default message to show if error response doesn't have a message
 * @returns {string} - Formatted error message
 */
export const handleApiError = (error, defaultMessage = 'An error occurred') => {
  // Check if it's an axios error with a response
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    const { data, status } = error.response;
    
    // If the server sent a message, use it
    if (data && data.message) {
      return `Error ${status}: ${data.message}`;
    }
    
    // Otherwise use a generic message based on status code
    return `Error ${status}: ${getStatusMessage(status)}`;
  } else if (error.request) {
    // The request was made but no response was received
    return 'No response received from server. Please check your connection.';
  } else {
    // Something happened in setting up the request that triggered an Error
    return error.message || defaultMessage;
  }
};

/**
 * Get a human-readable message for common HTTP status codes
 * @param {number} status - HTTP status code
 * @returns {string} - Human-readable message
 */
const getStatusMessage = (status) => {
  switch (status) {
    case 400:
      return 'Bad request';
    case 401:
      return 'Unauthorized. Please log in again.';
    case 403:
      return 'Forbidden. You do not have permission to access this resource.';
    case 404:
      return 'Resource not found';
    case 500:
      return 'Internal server error';
    default:
      return 'An error occurred';
  }
};
