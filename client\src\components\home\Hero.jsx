import { Link, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';

const Hero = () => {
  const navigate = useNavigate();
  const [backgroundIndex, setBackgroundIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');

  const backgrounds = [
    'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1543339308-43e59d6b73a6?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1606787366850-de6330128bfc?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80'
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setBackgroundIndex((prevIndex) => (prevIndex + 1) % backgrounds.length);
    }, 6000);

    return () => clearInterval(interval);
  }, [backgrounds.length]);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/recipes?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <section
      className="hero-section position-relative overflow-hidden text-white"
      style={{
        minHeight: '100vh',
        backgroundImage: `linear-gradient(135deg, rgba(255, 107, 107, 0.8), rgba(78, 84, 200, 0.8)), url(${backgrounds[backgroundIndex]})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed',
        transition: 'all 1.5s ease-in-out'
      }}
    >
      {/* Animated particles background */}
      <div className="particles-bg position-absolute w-100 h-100">
        <div className="particle particle-1"></div>
        <div className="particle particle-2"></div>
        <div className="particle particle-3"></div>
        <div className="particle particle-4"></div>
        <div className="particle particle-5"></div>
      </div>

      <div className="container h-100 d-flex flex-column justify-content-center align-items-center text-center position-relative">
        <div className="hero-content animate-fade-in-up">
          <h1 className="hero-title display-2 fw-bold mb-4 text-shadow">
            Discover Amazing
            <span className="text-gradient d-block">Food Recipes</span>
          </h1>
          <p className="hero-subtitle lead fs-4 mb-5 text-shadow opacity-90 mx-auto" style={{ maxWidth: '600px' }}>
            Explore thousands of delicious recipes from around the world. Cook, share, and enjoy!
          </p>

          {/* Enhanced Search Bar */}
          <div className="search-container mb-5 mx-auto" style={{ maxWidth: '600px' }}>
            <form onSubmit={handleSearch} className="search-form">
              <div className="search-input-group position-relative">
                <div className="search-icon position-absolute">
                  <i className="bi bi-search"></i>
                </div>
                <input
                  type="text"
                  className="search-input form-control form-control-lg"
                  placeholder="Search for recipes, ingredients, or cuisines..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button className="search-btn btn btn-primary btn-lg" type="submit">
                  <span className="d-none d-sm-inline">Search</span>
                  <i className="bi bi-arrow-right d-sm-none"></i>
                </button>
              </div>
            </form>

            {/* Popular searches */}
            <div className="popular-searches mt-3 d-none d-md-block">
              <span className="text-white-50 small me-3">Popular:</span>
              {['Pasta', 'Chicken', 'Vegetarian', 'Desserts', 'Quick Meals'].map((term, index) => (
                <Link
                  key={index}
                  to={`/recipes?search=${term.toLowerCase()}`}
                  className="popular-tag text-white text-decoration-none me-3 small"
                >
                  {term}
                </Link>
              ))}
            </div>
          </div>

          {/* Action buttons */}
          <div className="hero-actions d-flex flex-column flex-sm-row gap-3 justify-content-center">
            <Link to="/recipes" className="btn btn-primary btn-lg px-5 py-3 rounded-pill">
              <i className="bi bi-grid-3x3-gap me-2"></i>
              Explore Recipes
            </Link>
            <Link to="/add-recipe" className="btn btn-outline-light btn-lg px-5 py-3 rounded-pill">
              <i className="bi bi-plus-circle me-2"></i>
              Share Recipe
            </Link>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="scroll-indicator position-absolute bottom-0 start-50 translate-middle-x mb-4">
          <div className="scroll-arrow animate-bounce">
            <i className="bi bi-chevron-down fs-4"></i>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
