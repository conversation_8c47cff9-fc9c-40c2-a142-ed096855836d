import { Link, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';

const Hero = () => {
  const navigate = useNavigate();
  const [backgroundIndex, setBackgroundIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');

  const backgrounds = [
    'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80',
    'https://images.unsplash.com/photo-1543339308-43e59d6b73a6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80',
    'https://images.unsplash.com/photo-1606787366850-de6330128bfc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80'
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setBackgroundIndex((prevIndex) => (prevIndex + 1) % backgrounds.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [backgrounds.length]);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/recipes?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <div
      className="hero-section position-relative overflow-hidden text-white"
      style={{
        minHeight: '500px',
        height: 'auto',
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url(${backgrounds[backgroundIndex]})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        transition: 'background-image 1s ease-in-out'
      }}
    >
      <div className="container py-5 d-flex flex-column justify-content-center align-items-center text-center">
        <h1 className="display-4 display-md-3 fw-bold mb-3 mb-md-4">Discover Delicious Recipes</h1>
        <p className="lead mb-4 px-2">
          Find inspiration for your next meal with our collection of recipes from around the world
        </p>

        {/* Search Bar */}
        <div className="search-container w-100 w-md-75 px-3 px-md-0 mb-4 mb-md-5 animate__animated animate__fadeInUp">
          <form onSubmit={handleSearch} className="d-flex">
            <div className="input-group">
              <input
                type="text"
                className="form-control"
                placeholder="Search recipes..."
                aria-label="Search recipes"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <button className="btn btn-primary" type="submit">
                <i className="bi bi-search d-md-none"></i>
                <span className="d-none d-md-inline"><i className="bi bi-search me-2"></i>Search</span>
              </button>
            </div>
          </form>
          <div className="search-suggestions mt-2 text-white-50 d-none d-md-block">
            <small>
              Popular searches:
              <Link to="/recipes?search=pasta" className="text-white ms-1 me-2">Pasta</Link>
              <Link to="/recipes?search=chicken" className="text-white me-2">Chicken</Link>
              <Link to="/recipes?search=vegetarian" className="text-white me-2">Vegetarian</Link>
              <Link to="/recipes?search=dessert" className="text-white">Desserts</Link>
            </small>
          </div>
        </div>

        <div className="d-flex flex-column flex-sm-row gap-2 gap-sm-3">
          <Link to="/recipes" className="btn btn-primary px-4 py-2">
            <i className="bi bi-grid me-2"></i>Browse Recipes
          </Link>
          <Link to="/add-recipe" className="btn btn-outline-light px-4 py-2">
            <i className="bi bi-plus-circle me-2"></i>Share Your Recipe
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Hero;
