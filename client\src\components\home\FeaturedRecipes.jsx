import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getRecipes } from '../../services/api';

const FeaturedRecipes = () => {
  const [recipes, setRecipes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchRecipes = async () => {
      try {
        const data = await getRecipes();
        setRecipes(data.slice(0, 6)); // Get first 6 recipes for featured section
        setLoading(false);
      } catch (err) {
        console.error('Error fetching recipes:', err);
        setError('Failed to fetch recipes');
        // Enhanced fallback data
        setRecipes([
          {
            _id: '1',
            title: 'Truffle Risotto',
            description: 'Creamy Italian risotto with aromatic truffle oil and parmesan cheese.',
            imageUrl: 'https://images.unsplash.com/photo-1476124369491-e7addf5db371?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            cookingTime: 35,
            difficulty: 'Hard',
            servings: 4,
            rating: 4.8,
            author: 'Chef <PERSON>'
          },
          {
            _id: '2',
            title: 'Salmon Teriyaki Bowl',
            description: 'Fresh grilled salmon with teriyaki glaze served over steamed rice.',
            imageUrl: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            cookingTime: 25,
            difficulty: 'Medium',
            servings: 2,
            rating: 4.6,
            author: 'Chef Yuki'
          },
          {
            _id: '3',
            title: 'Chocolate Lava Cake',
            description: 'Decadent chocolate cake with a molten center, served with vanilla ice cream.',
            imageUrl: 'https://images.unsplash.com/photo-1606313564200-e75d5e30476c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            cookingTime: 20,
            difficulty: 'Medium',
            servings: 2,
            rating: 4.9,
            author: 'Chef Sophie'
          }
        ]);
        setLoading(false);
      }
    };

    fetchRecipes();
  }, []);

  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy': return 'success';
      case 'medium': return 'warning';
      case 'hard': return 'danger';
      default: return 'secondary';
    }
  };

  const formatCookingTime = (minutes) => {
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${minutes}m`;
  };

  if (loading) {
    return (
      <section className="featured-recipes-section py-5">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="display-4 fw-bold">Featured Recipes</h2>
          </div>
          <div className="row g-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="col-12 col-md-6 col-lg-4">
                <div className="recipe-card h-100 rounded-4 overflow-hidden shadow-sm">
                  <div className="placeholder-glow">
                    <div className="placeholder bg-light" style={{ height: '250px' }}></div>
                  </div>
                  <div className="card-body p-4">
                    <div className="placeholder-glow">
                      <h5 className="placeholder col-8 mb-3"></h5>
                      <p className="placeholder col-12 mb-2"></p>
                      <p className="placeholder col-6"></p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="featured-recipes-section py-5 bg-light">
      <div className="container">
        <div className="text-center mb-5">
          <span className="badge bg-primary-soft text-primary px-3 py-2 rounded-pill mb-3">
            Chef's Choice
          </span>
          <h2 className="display-4 fw-bold text-dark mb-3">
            Featured <span className="text-primary">Recipes</span>
          </h2>
          <p className="lead text-muted mx-auto" style={{ maxWidth: '600px' }}>
            Handpicked by our culinary experts, these recipes showcase the best flavors and techniques.
          </p>
        </div>

        <div className="row g-4">
          {recipes.length > 0 ? (
            recipes.slice(0, 6).map((recipe, index) => (
              <div key={recipe._id} className="col-12 col-md-6 col-lg-4">
                <div
                  className="recipe-card h-100 bg-white rounded-4 overflow-hidden shadow-hover position-relative"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {/* Recipe Image */}
                  <div className="recipe-image-container position-relative overflow-hidden">
                    <img
                      src={recipe.imageUrl || 'https://via.placeholder.com/400x250?text=Recipe'}
                      className="recipe-image w-100"
                      alt={recipe.title}
                      style={{ height: '250px', objectFit: 'cover', transition: 'transform 0.3s ease' }}
                    />

                    {/* Difficulty Badge */}
                    <div className="position-absolute top-0 start-0 m-3">
                      <span className={`badge bg-${getDifficultyColor(recipe.difficulty)} px-3 py-2 rounded-pill`}>
                        {recipe.difficulty}
                      </span>
                    </div>

                    {/* Rating Badge */}
                    {recipe.rating && (
                      <div className="position-absolute top-0 end-0 m-3">
                        <span className="badge bg-dark bg-opacity-75 text-white px-3 py-2 rounded-pill">
                          <i className="bi bi-star-fill text-warning me-1"></i>
                          {recipe.rating}
                        </span>
                      </div>
                    )}

                    {/* Hover Overlay */}
                    <div className="recipe-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
                      <Link
                        to={`/recipes/${recipe._id}`}
                        className="btn btn-light btn-lg rounded-pill px-4"
                      >
                        <i className="bi bi-eye me-2"></i>
                        View Recipe
                      </Link>
                    </div>
                  </div>

                  {/* Recipe Content */}
                  <div className="card-body p-4">
                    <div className="d-flex justify-content-between align-items-start mb-2">
                      <h5 className="recipe-title fw-bold mb-0 text-dark">{recipe.title}</h5>
                      <button className="btn btn-link p-0 text-muted">
                        <i className="bi bi-heart fs-5"></i>
                      </button>
                    </div>

                    <p className="recipe-description text-muted mb-3 small">
                      {recipe.description.length > 80 ? `${recipe.description.substring(0, 80)}...` : recipe.description}
                    </p>

                    {/* Recipe Meta */}
                    <div className="recipe-meta d-flex justify-content-between align-items-center mb-3">
                      <div className="d-flex align-items-center text-muted small">
                        <i className="bi bi-clock me-1"></i>
                        {formatCookingTime(recipe.cookingTime)}
                      </div>
                      <div className="d-flex align-items-center text-muted small">
                        <i className="bi bi-people me-1"></i>
                        {recipe.servings} servings
                      </div>
                    </div>

                    {/* Author */}
                    {recipe.author && (
                      <div className="recipe-author d-flex align-items-center mb-3">
                        <div className="author-avatar bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style={{ width: '32px', height: '32px' }}>
                          <i className="bi bi-person text-white small"></i>
                        </div>
                        <span className="small text-muted">by {recipe.author}</span>
                      </div>
                    )}

                    {/* Action Button */}
                    <Link
                      to={`/recipes/${recipe._id}`}
                      className="btn btn-primary w-100 rounded-pill"
                    >
                      <i className="bi bi-arrow-right me-2"></i>
                      View Full Recipe
                    </Link>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-12 text-center py-5">
              <div className="empty-state">
                <i className="bi bi-journal-bookmark fs-1 text-muted mb-3"></i>
                <h4 className="text-muted mb-3">No recipes found</h4>
                <p className="text-muted mb-4">Be the first to share a delicious recipe with our community!</p>
                <Link to="/add-recipe" className="btn btn-primary btn-lg rounded-pill px-5">
                  <i className="bi bi-plus-circle me-2"></i>
                  Add Your First Recipe
                </Link>
              </div>
            </div>
          )}
        </div>

        {/* View All Button */}
        {recipes.length > 0 && (
          <div className="text-center mt-5">
            <Link to="/recipes" className="btn btn-outline-primary btn-lg px-5 py-3 rounded-pill">
              <i className="bi bi-grid-3x3-gap me-2"></i>
              Explore All Recipes
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default FeaturedRecipes;
