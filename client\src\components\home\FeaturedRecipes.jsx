import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getRecipes } from '../../services/api';

const FeaturedRecipes = () => {
  const [recipes, setRecipes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchRecipes = async () => {
      try {
        const data = await getRecipes();
        setRecipes(data.slice(0, 3)); // Get only the first 3 recipes
        setLoading(false);
      } catch (err) {
        console.error('Error fetching recipes:', err);
        setError('Failed to fetch recipes');
        setLoading(false);
      }
    };

    fetchRecipes();
  }, []);

  if (loading) {
    return (
      <div className="container my-5">
        <h2 className="text-center mb-4">Featured Recipes</h2>
        <div className="d-flex justify-content-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container my-5">
        <h2 className="text-center mb-4">Featured Recipes</h2>
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="container my-4 my-md-5 px-3 px-md-3">
      <h2 className="text-center mb-3 mb-md-4">Featured Recipes</h2>
      <div className="row">
        {recipes.length > 0 ? (
          recipes.map((recipe) => (
            <div className="col-12 col-sm-6 col-lg-4 mb-4" key={recipe._id}>
              <div className="card h-100 shadow-sm">
                <img
                  src={recipe.imageUrl || 'https://via.placeholder.com/300x200?text=No+Image'}
                  className="card-img-top"
                  alt={recipe.title}
                  style={{ height: '180px', objectFit: 'cover' }}
                />
                <div className="card-body d-flex flex-column">
                  <h5 className="card-title h6 h5-md">{recipe.title}</h5>
                  <p className="card-text text-muted mb-2">
                    <small>
                      <i className="bi bi-clock me-1"></i> {recipe.cookingTime} mins<span className="d-none d-md-inline"> |
                      <i className="bi bi-people ms-2 me-1"></i> {recipe.servings} servings |
                      <i className="bi bi-bar-chart ms-2 me-1"></i> {recipe.difficulty}</span>
                    </small>
                  </p>
                  <div className="d-md-none mb-2">
                    <span className="badge bg-light text-dark me-2">
                      <i className="bi bi-people me-1"></i> {recipe.servings}
                    </span>
                    <span className="badge bg-light text-dark">
                      {recipe.difficulty}
                    </span>
                  </div>
                  <p className="card-text flex-grow-1 small">{recipe.description.substring(0, 80)}...</p>
                  <Link to={`/recipes/${recipe._id}`} className="btn btn-sm btn-outline-primary mt-auto w-100">
                    View Recipe
                  </Link>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-12 text-center">
            <p>No recipes found. Be the first to add a recipe!</p>
            <Link to="/add-recipe" className="btn btn-primary">
              Add Recipe
            </Link>
          </div>
        )}
      </div>
      {recipes.length > 0 && (
        <div className="text-center mt-3 mt-md-4">
          <Link to="/recipes" className="btn btn-primary">
            View All Recipes
          </Link>
        </div>
      )}
    </div>
  );
};

export default FeaturedRecipes;
