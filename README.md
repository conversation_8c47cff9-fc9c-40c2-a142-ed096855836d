# 🍳 Food Recipe Application

A comprehensive full-stack web application for managing and sharing food recipes, built with the MERN stack (MongoDB, Express.js, React, Node.js).

## ✨ Features

### 🏠 Advanced Home Page
- **Hero Section** with dynamic background and search functionality
- **Recipe of the Day** showcase with special highlighting
- **Featured Recipes** with detailed information cards
- **Popular Recipes** with view counts and rankings
- **Recipe Categories** for easy browsing
- **Cooking Tips & Tricks** section with expert advice
- **User Testimonials** carousel with real user feedback
- **Social Media Integration** for sharing recipes
- **Newsletter Subscription** for updates

### 🔐 Authentication & User Management
- User registration and login with validation
- JWT-based secure authentication
- Protected routes and middleware
- User profile management with favorites
- Password encryption with bcryptjs

### 📱 Recipe Management
- Browse all recipes with advanced filtering
- Search by title, ingredients, or category
- View detailed recipe information with images
- Add new recipes with rich form validation
- Edit and delete existing recipes
- Import recipes from external APIs (TheMealDB)
- Recipe difficulty levels and cooking times

### 📱 Fully Responsive Design
- Mobile-first approach with touch-friendly interface
- Tablet and desktop optimized layouts
- Adaptive components for all screen sizes
- Bootstrap 5 integration with custom styling

## 🛠️ Technology Stack

### Frontend
- **React 18** - Modern UI library with hooks
- **React Router 6** - Client-side routing
- **Axios** - HTTP client for API calls
- **Bootstrap 5** - Responsive CSS framework
- **Vite** - Fast build tool and dev server

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web application framework
- **MongoDB** - NoSQL database
- **Mongoose** - MongoDB object modeling
- **JWT** - JSON Web Token authentication
- **bcryptjs** - Password hashing

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- npm (v6 or higher)
- MongoDB Atlas account (already configured)

### 🎯 One-Click Setup

**Windows Users:**
```bash
# Run the automated setup script
start-project.bat
```

**Manual Setup:**
```bash
# 1. Install server dependencies
cd server
npm install

# 2. Install client dependencies
cd ../client
npm install

# 3. Start the application
cd ../server
npm run dev

# 4. In a new terminal, start the frontend
cd client
npm run dev
```

### 🌐 Access Points
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5001
- **Connection Test**: http://localhost:5173/test-connection

### Configuration

#### Database Configuration

The application is configured to connect to a local MongoDB instance by default. You can modify the connection settings in the `.env` file in the server directory:

```
PORT=5001
MONGO_URI=mongodb://localhost:27017/food-recipe-app
NODE_ENV=development
```

If you want to use MongoDB Atlas (cloud service) instead:

1. Create a free account at [MongoDB Atlas](https://www.mongodb.com/cloud/atlas/register)
2. Create a new cluster
3. Get your connection string and update it in the `.env` file

#### API Configuration

The frontend is configured to connect to the backend API at `http://localhost:5001/api`. You can modify this in the `.env` file in the client directory:

```
VITE_API_URL=http://localhost:5001/api
```

### Running the Application

#### Starting MongoDB (Local Installation)

Before running the application, make sure MongoDB is running. You can use the provided script:

```bash
# Start MongoDB
./start-mongodb.bat
```

#### Starting the Application

You can start both the frontend and backend servers with a single command:

```bash
# Start both servers
./start-app.bat
```

Or you can start them separately:

```bash
# Start the backend server
cd server
npm run dev

# Start the frontend server (in a new terminal)
cd client
npm run dev
```

## Usage

- Frontend: [http://localhost:5173](http://localhost:5173)
- Backend API: [http://localhost:5001](http://localhost:5001)
- Connection Test: [http://localhost:5173/test-connection](http://localhost:5173/test-connection)

## API Endpoints

### Recipes
- `GET /api/recipes` - Get all recipes (with optional filtering)
- `GET /api/recipes/:id` - Get a specific recipe
- `POST /api/recipes` - Create a new recipe
- `PUT /api/recipes/:id` - Update a recipe
- `DELETE /api/recipes/:id` - Delete a recipe

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login a user
- `GET /api/auth/profile` - Get user profile (protected)
- `PUT /api/auth/profile` - Update user profile (protected)
- `GET /api/auth/favorites` - Get user's favorite recipes (protected)
- `POST /api/auth/favorites/:id` - Add recipe to favorites (protected)
- `DELETE /api/auth/favorites/:id` - Remove recipe from favorites (protected)

## Troubleshooting

### MongoDB Connection Issues

If you encounter issues connecting to MongoDB:

1. Make sure MongoDB is installed and running
2. Check if the MongoDB connection string in `.env` is correct
3. If using a local MongoDB, ensure the MongoDB service is running
4. If using MongoDB Atlas, check your network connection and whitelist your IP address

### API Connection Issues

If the frontend cannot connect to the backend:

1. Make sure both servers are running
2. Check if the API URL in the client's `.env` file is correct
3. Verify that CORS is properly configured in the backend
4. Check the browser console for any error messages

## License

MIT
