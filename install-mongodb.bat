@echo off
echo MongoDB Installation Guide for Food Recipe App
echo ==============================================
echo.
echo This script will guide you through the process of installing MongoDB on Windows.
echo.
echo Step 1: Download MongoDB Community Edition
echo -----------------------------------------
echo Please download MongoDB Community Edition from:
echo https://www.mongodb.com/try/download/community
echo.
echo Choose the following options:
echo - Version: Latest version
echo - Platform: Windows
echo - Package: MSI
echo.
echo Press any key to open the download page in your browser...
pause > nul
start "" "https://www.mongodb.com/try/download/community"
echo.

echo Step 2: Install MongoDB
echo ---------------------
echo 1. Run the downloaded MSI file
echo 2. Follow the installation wizard
echo 3. Choose "Complete" installation
echo 4. Install MongoDB Compass (optional but recommended)
echo.
echo Press any key when you have completed the installation...
pause > nul
echo.

echo Step 3: Add MongoDB to PATH
echo ------------------------
echo To make MongoDB commands available in the command prompt, you need to add
echo the MongoDB bin directory to your system PATH.
echo.
echo 1. Right-click on "This PC" or "My Computer" and select "Properties"
echo 2. Click on "Advanced system settings"
echo 3. Click on "Environment Variables"
echo 4. Under "System variables", find and select "Path", then click "Edit"
echo 5. Click "New" and add the path to MongoDB bin directory
echo    (typically C:\Program Files\MongoDB\Server\[version]\bin)
echo.
echo Press any key when you have completed this step...
pause > nul
echo.

echo Step 4: Verify Installation
echo ------------------------
echo Let's verify that MongoDB is properly installed.
echo.
echo Running "mongod --version"...
mongod --version
echo.
if %ERRORLEVEL% NEQ 0 (
    echo MongoDB is not properly installed or not in your PATH.
    echo Please complete the steps above and try again.
) else (
    echo MongoDB is successfully installed!
)
echo.

echo Step 5: Start MongoDB
echo ------------------
echo Now you can start MongoDB using the start-mongodb.bat script.
echo.
echo Press any key to exit...
pause > nul
