const User = require('../models/User');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');

// Generate JWT token
const generateToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET || 'your_jwt_secret', {
    expiresIn: '30d'
  });
};

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
const registerUser = async (req, res) => {
  try {
    const { username, email, password } = req.body;

    // Check if MongoDB is connected
    const isConnected = mongoose.connection.readyState === 1;

    if (isConnected) {
      // MongoDB is connected, proceed with normal registration
      console.log('MongoDB is connected, proceeding with normal registration');

      // Check if user already exists
      const userExists = await User.findOne({ $or: [{ email }, { username }] });
      if (userExists) {
        return res.status(400).json({ message: 'User already exists' });
      }

      // Create new user
      const user = await User.create({
        username,
        email,
        password
      });

      if (user) {
        res.status(201).json({
          _id: user._id,
          username: user.username,
          email: user.email,
          token: generateToken(user._id)
        });
      } else {
        res.status(400).json({ message: 'Invalid user data' });
      }
    } else {
      // MongoDB is not connected, use mock user data
      console.log('MongoDB is not connected, using mock user data for registration');

      // Generate a mock user ID
      const mockUserId = Date.now().toString();

      // Return a successful response with mock data
      res.status(201).json({
        _id: mockUserId,
        username,
        email,
        token: generateToken(mockUserId)
      });
    }
  } catch (error) {
    console.error('Error registering user:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Authenticate user & get token
// @route   POST /api/auth/login
// @access  Public
const loginUser = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Check if MongoDB is connected
    const isConnected = mongoose.connection.readyState === 1;

    if (isConnected) {
      // MongoDB is connected, proceed with normal login
      console.log('MongoDB is connected, proceeding with normal login');

      // Find user by email
      const user = await User.findOne({ email });

      // Check if user exists and password matches
      if (user && (await user.comparePassword(password))) {
        res.json({
          _id: user._id,
          username: user.username,
          email: user.email,
          token: generateToken(user._id)
        });
      } else {
        res.status(401).json({ message: 'Invalid email or password' });
      }
    } else {
      // MongoDB is not connected, use mock user data
      console.log('MongoDB is not connected, using mock user data for login');

      // For demo purposes, accept any login when MongoDB is not connected
      // In a real app, you might want to have some predefined mock users
      const mockUserId = Date.now().toString();

      res.json({
        _id: mockUserId,
        username: email.split('@')[0], // Use part of email as username
        email: email,
        token: generateToken(mockUserId)
      });
    }
  } catch (error) {
    console.error('Error logging in:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get user profile
// @route   GET /api/auth/profile
// @access  Private
const getUserProfile = async (req, res) => {
  try {
    // Check if MongoDB is connected
    const isConnected = mongoose.connection.readyState === 1;

    if (isConnected) {
      // MongoDB is connected, proceed with normal profile retrieval
      console.log('MongoDB is connected, proceeding with normal profile retrieval');

      const user = await User.findById(req.user._id).select('-password');

      if (user) {
        res.json(user);
      } else {
        res.status(404).json({ message: 'User not found' });
      }
    } else {
      // MongoDB is not connected, return mock user data
      console.log('MongoDB is not connected, returning mock user profile');

      // Return the user data from the JWT token
      res.json({
        _id: req.user._id,
        username: req.user.username || 'User',
        email: req.user.email || '<EMAIL>',
        favorites: []
      });
    }
  } catch (error) {
    console.error('Error getting user profile:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update user profile
// @route   PUT /api/auth/profile
// @access  Private
const updateUserProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user._id);

    if (user) {
      user.username = req.body.username || user.username;
      user.email = req.body.email || user.email;

      if (req.body.password) {
        user.password = req.body.password;
      }

      const updatedUser = await user.save();

      res.json({
        _id: updatedUser._id,
        username: updatedUser.username,
        email: updatedUser.email,
        token: generateToken(updatedUser._id)
      });
    } else {
      res.status(404).json({ message: 'User not found' });
    }
  } catch (error) {
    console.error('Error updating user profile:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Add recipe to favorites
// @route   POST /api/auth/favorites/:id
// @access  Private
const addToFavorites = async (req, res) => {
  try {
    const user = await User.findById(req.user._id);
    const recipeId = req.params.id;

    if (user) {
      // Check if recipe is already in favorites
      if (user.favorites.includes(recipeId)) {
        return res.status(400).json({ message: 'Recipe already in favorites' });
      }

      user.favorites.push(recipeId);
      await user.save();

      res.json({ message: 'Recipe added to favorites' });
    } else {
      res.status(404).json({ message: 'User not found' });
    }
  } catch (error) {
    console.error('Error adding to favorites:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Remove recipe from favorites
// @route   DELETE /api/auth/favorites/:id
// @access  Private
const removeFromFavorites = async (req, res) => {
  try {
    const user = await User.findById(req.user._id);
    const recipeId = req.params.id;

    if (user) {
      // Check if recipe is in favorites
      if (!user.favorites.includes(recipeId)) {
        return res.status(400).json({ message: 'Recipe not in favorites' });
      }

      user.favorites = user.favorites.filter(id => id.toString() !== recipeId);
      await user.save();

      res.json({ message: 'Recipe removed from favorites' });
    } else {
      res.status(404).json({ message: 'User not found' });
    }
  } catch (error) {
    console.error('Error removing from favorites:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get user favorites
// @route   GET /api/auth/favorites
// @access  Private
const getFavorites = async (req, res) => {
  try {
    const user = await User.findById(req.user._id).populate('favorites');

    if (user) {
      res.json(user.favorites);
    } else {
      res.status(404).json({ message: 'User not found' });
    }
  } catch (error) {
    console.error('Error getting favorites:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  addToFavorites,
  removeFromFavorites,
  getFavorites
};
