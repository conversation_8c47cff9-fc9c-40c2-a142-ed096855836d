import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getRecipes } from '../../services/api';

const RecipeOfTheDay = () => {
  const [recipe, setRecipe] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchRecipeOfTheDay = async () => {
      try {
        // Get all recipes
        const recipes = await getRecipes();

        if (recipes && recipes.length > 0) {
          // Use a deterministic way to select a "recipe of the day" based on the date
          // This ensures the same recipe is shown throughout the day
          const today = new Date();
          const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / (1000 * 60 * 60 * 24));
          const recipeIndex = dayOfYear % recipes.length;

          setRecipe(recipes[recipeIndex]);
        }
        setLoading(false);
      } catch (err) {
        console.error('Error fetching recipe of the day:', err);
        setError('Failed to fetch recipe of the day');
        setLoading(false);
      }
    };

    fetchRecipeOfTheDay();
  }, []);

  if (loading) {
    return (
      <div className="container my-5">
        <div className="row">
          <div className="col-12">
            <div className="card shadow-sm">
              <div className="card-body text-center p-5">
                <h3 className="card-title mb-4">Recipe of the Day</h3>
                <div className="spinner-border text-primary" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !recipe) {
    return (
      <div className="container my-5">
        <div className="row">
          <div className="col-12">
            <div className="card shadow-sm">
              <div className="card-body text-center p-5">
                <h3 className="card-title mb-4">Recipe of the Day</h3>
                <p className="text-muted">
                  {error || "No recipe available today. Check back tomorrow!"}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container my-4 my-md-5 recipe-of-the-day px-3 px-md-3">
      <div className="row">
        <div className="col-12">
          <div className="card border-0 shadow-sm overflow-hidden">
            <div className="ribbon ribbon-top-right d-none d-md-block">
              <span>Today's Pick</span>
            </div>
            <div className="row g-0 flex-column flex-md-row">
              <div className="col-12 col-md-6">
                <div className="position-relative">
                  <div className="ribbon-mobile d-md-none position-absolute top-0 end-0 bg-danger text-white px-3 py-1 m-2 rounded-pill">
                    Today's Pick
                  </div>
                  <img
                    src={recipe.imageUrl || 'https://via.placeholder.com/600x400?text=No+Image'}
                    className="img-fluid w-100"
                    alt={recipe.title}
                    style={{ objectFit: 'cover', maxHeight: '300px' }}
                  />
                </div>
              </div>
              <div className="col-12 col-md-6">
                <div className="card-body p-3 p-md-4 p-lg-5">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <h6 className="text-primary mb-0">
                      <i className="bi bi-calendar-star me-2"></i>Recipe of the Day
                    </h6>
                    <span className="badge bg-light text-dark">
                      {recipe.difficulty}
                    </span>
                  </div>
                  <h2 className="card-title h3 h2-md mb-2 mb-md-3">{recipe.title}</h2>
                  <p className="card-text mb-3 mb-md-4">{recipe.description}</p>

                  <div className="d-flex justify-content-between mb-3 mb-md-4">
                    <div className="text-center">
                      <i className="bi bi-clock fs-5 fs-md-4 d-block mb-1 text-primary"></i>
                      <span className="small">{recipe.cookingTime} mins</span>
                    </div>
                    <div className="text-center">
                      <i className="bi bi-people fs-5 fs-md-4 d-block mb-1 text-primary"></i>
                      <span className="small">{recipe.servings} servings</span>
                    </div>
                    <div className="text-center">
                      <i className="bi bi-bar-chart fs-5 fs-md-4 d-block mb-1 text-primary"></i>
                      <span className="small">{recipe.difficulty}</span>
                    </div>
                  </div>

                  <h5 className="mb-2 mb-md-3 fs-6 fs-md-5">Key Ingredients:</h5>
                  <div className="mb-3 mb-md-4">
                    <div className="row">
                      {recipe.ingredients.slice(0, 4).map((ingredient, index) => (
                        <div className="col-12 col-sm-6 mb-1 mb-md-2" key={index}>
                          <div className="d-flex align-items-center">
                            <i className="bi bi-check-circle-fill text-success me-2"></i>
                            <span className="small">{ingredient}</span>
                          </div>
                        </div>
                      ))}
                      {recipe.ingredients.length > 4 && (
                        <div className="col-12">
                          <small className="text-muted">And {recipe.ingredients.length - 4} more ingredients...</small>
                        </div>
                      )}
                    </div>
                  </div>

                  <Link to={`/recipes/${recipe._id}`} className="btn btn-primary w-100 w-md-auto">
                    View Full Recipe
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecipeOfTheDay;
