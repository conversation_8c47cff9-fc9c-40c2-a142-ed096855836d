import { useState, useEffect } from 'react';
import { getRecipes } from '../services/api';
import { isAuthenticated, getCurrentUser } from '../services/auth';

const ConnectionTest = () => {
  const [status, setStatus] = useState('Checking connection...');
  const [recipes, setRecipes] = useState([]);
  const [error, setError] = useState(null);
  const [user, setUser] = useState(null);

  useEffect(() => {
    const testConnection = async () => {
      try {
        // Check if user is authenticated
        const authenticated = isAuthenticated();
        if (authenticated) {
          setUser(getCurrentUser());
        }

        // Test API connection by fetching recipes
        const data = await getRecipes();
        setRecipes(data);
        setStatus('Connected to backend successfully!');
      } catch (error) {
        console.error('Connection test failed:', error);
        setError(error.message || 'Failed to connect to backend');
        setStatus('Connection failed');
      }
    };

    testConnection();
  }, []);

  return (
    <div className="container mt-5">
      <div className="card">
        <div className="card-header bg-primary text-white">
          <h3>Backend Connection Test</h3>
        </div>
        <div className="card-body">
          <div className="mb-4">
            <h4>Connection Status:</h4>
            <div className={`alert ${status.includes('success') ? 'alert-success' : error ? 'alert-danger' : 'alert-info'}`}>
              {status}
            </div>
            {error && (
              <div className="alert alert-danger">
                <strong>Error:</strong> {error}
              </div>
            )}
          </div>

          {user && (
            <div className="mb-4">
              <h4>Authenticated User:</h4>
              <div className="alert alert-info">
                <p><strong>Username:</strong> {user.username}</p>
                <p><strong>Email:</strong> {user.email}</p>
              </div>
            </div>
          )}

          <div>
            <h4>Recipes from Backend:</h4>
            {recipes.length > 0 ? (
              <ul className="list-group">
                {recipes.slice(0, 3).map((recipe) => (
                  <li key={recipe._id} className="list-group-item">
                    <h5>{recipe.title}</h5>
                    <p>{recipe.description}</p>
                  </li>
                ))}
                {recipes.length > 3 && (
                  <li className="list-group-item text-muted">
                    ...and {recipes.length - 3} more recipes
                  </li>
                )}
              </ul>
            ) : (
              <p>No recipes found or still loading...</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConnectionTest;
