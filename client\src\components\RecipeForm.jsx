import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getRecipeById, createRecipe, updateRecipe } from '../services/api';
import { searchMealsByName, getMealById, convertMealToRecipe } from '../services/mealdb';

const RecipeForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(id ? true : false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searching, setSearching] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    ingredients: [''],
    instructions: '',
    cookingTime: '',
    servings: '',
    difficulty: 'Medium',
    imageUrl: ''
  });

  useEffect(() => {
    if (id) {
      const fetchRecipe = async () => {
        try {
          const data = await getRecipeById(id);
          setFormData({
            title: data.title,
            description: data.description,
            ingredients: data.ingredients,
            instructions: data.instructions,
            cookingTime: data.cookingTime,
            servings: data.servings,
            difficulty: data.difficulty,
            imageUrl: data.imageUrl
          });
          setLoading(false);
        } catch (err) {
          setError('Failed to fetch recipe details');
          setLoading(false);
        }
      };

      fetchRecipe();
    }
  }, [id]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleIngredientChange = (index, value) => {
    const updatedIngredients = [...formData.ingredients];
    updatedIngredients[index] = value;
    setFormData({
      ...formData,
      ingredients: updatedIngredients
    });
  };

  const addIngredientField = () => {
    setFormData({
      ...formData,
      ingredients: [...formData.ingredients, '']
    });
  };

  const removeIngredientField = (index) => {
    const updatedIngredients = [...formData.ingredients];
    updatedIngredients.splice(index, 1);
    setFormData({
      ...formData,
      ingredients: updatedIngredients
    });
  };

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchTerm.trim()) return;

    setSearching(true);
    try {
      const results = await searchMealsByName(searchTerm);
      setSearchResults(results || []);
    } catch (err) {
      setError('Failed to search recipes');
    } finally {
      setSearching(false);
    }
  };

  const importRecipe = async (mealId) => {
    setLoading(true);
    try {
      const mealData = await getMealById(mealId);
      if (mealData) {
        const recipe = convertMealToRecipe(mealData);
        setFormData({
          title: recipe.title,
          description: recipe.description,
          ingredients: recipe.ingredients,
          instructions: recipe.instructions,
          cookingTime: recipe.cookingTime,
          servings: recipe.servings,
          difficulty: recipe.difficulty,
          imageUrl: recipe.imageUrl
        });
        setSearchResults([]);
        setSearchTerm('');
      }
    } catch (err) {
      setError('Failed to import recipe');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);

    // Filter out empty ingredients
    const filteredIngredients = formData.ingredients.filter(ing => ing.trim() !== '');

    try {
      const recipeData = {
        ...formData,
        ingredients: filteredIngredients,
        cookingTime: parseInt(formData.cookingTime),
        servings: parseInt(formData.servings)
      };

      if (id) {
        await updateRecipe(id, recipeData);
      } else {
        await createRecipe(recipeData);
      }

      navigate('/recipes');
    } catch (err) {
      setError('Failed to save recipe');
    }
  };

  if (loading) {
    return <div className="text-center mt-5"><div className="spinner-border"></div></div>;
  }

  return (
    <div className="recipe-form-container">
      <h2>{id ? 'Edit Recipe' : 'Add New Recipe'}</h2>

      {error && <div className="alert alert-danger">{error}</div>}

      {!id && (
        <div className="card mb-4">
          <div className="card-body">
            <h5 className="card-title">Import Recipe from TheMealDB</h5>
            <form onSubmit={handleSearch} className="d-flex mb-3">
              <input
                type="text"
                className="form-control me-2"
                placeholder="Search for recipes to import..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <button
                type="submit"
                className="btn btn-primary"
                disabled={searching}
              >
                {searching ? 'Searching...' : 'Search'}
              </button>
            </form>

            {searchResults.length > 0 && (
              <div className="list-group">
                {searchResults.slice(0, 5).map(meal => (
                  <button
                    key={meal.idMeal}
                    type="button"
                    className="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                    onClick={() => importRecipe(meal.idMeal)}
                  >
                    <div className="d-flex align-items-center">
                      <img
                        src={meal.strMealThumb}
                        alt={meal.strMeal}
                        className="me-3"
                        style={{ width: '50px', height: '50px', objectFit: 'cover', borderRadius: '4px' }}
                      />
                      <div>
                        <h6 className="mb-0">{meal.strMeal}</h6>
                        <small className="text-muted">{meal.strCategory} | {meal.strArea}</small>
                      </div>
                    </div>
                    <span className="btn btn-sm btn-outline-primary">Import</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-3">
          <label htmlFor="title" className="form-label">Recipe Title</label>
          <input
            type="text"
            className="form-control"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            required
          />
        </div>

        <div className="mb-3">
          <label htmlFor="description" className="form-label">Description</label>
          <textarea
            className="form-control"
            id="description"
            name="description"
            rows="3"
            value={formData.description}
            onChange={handleChange}
            required
          ></textarea>
        </div>

        <div className="mb-3">
          <label className="form-label">Ingredients</label>
          {formData.ingredients.map((ingredient, index) => (
            <div className="input-group mb-2" key={index}>
              <input
                type="text"
                className="form-control"
                value={ingredient}
                onChange={(e) => handleIngredientChange(index, e.target.value)}
                placeholder="Enter ingredient"
              />
              <button
                type="button"
                className="btn btn-outline-danger"
                onClick={() => removeIngredientField(index)}
                disabled={formData.ingredients.length <= 1}
              >
                Remove
              </button>
            </div>
          ))}
          <button
            type="button"
            className="btn btn-outline-primary btn-sm"
            onClick={addIngredientField}
          >
            Add Ingredient
          </button>
        </div>

        <div className="mb-3">
          <label htmlFor="instructions" className="form-label">Instructions</label>
          <textarea
            className="form-control"
            id="instructions"
            name="instructions"
            rows="5"
            value={formData.instructions}
            onChange={handleChange}
            placeholder="Enter step-by-step instructions. Use a new line for each step."
            required
          ></textarea>
        </div>

        <div className="row">
          <div className="col-md-4 mb-3">
            <label htmlFor="cookingTime" className="form-label">Cooking Time (minutes)</label>
            <input
              type="number"
              className="form-control"
              id="cookingTime"
              name="cookingTime"
              value={formData.cookingTime}
              onChange={handleChange}
              min="1"
              required
            />
          </div>

          <div className="col-md-4 mb-3">
            <label htmlFor="servings" className="form-label">Servings</label>
            <input
              type="number"
              className="form-control"
              id="servings"
              name="servings"
              value={formData.servings}
              onChange={handleChange}
              min="1"
              required
            />
          </div>

          <div className="col-md-4 mb-3">
            <label htmlFor="difficulty" className="form-label">Difficulty</label>
            <select
              className="form-select"
              id="difficulty"
              name="difficulty"
              value={formData.difficulty}
              onChange={handleChange}
            >
              <option value="Easy">Easy</option>
              <option value="Medium">Medium</option>
              <option value="Hard">Hard</option>
            </select>
          </div>
        </div>

        <div className="mb-3">
          <label htmlFor="imageUrl" className="form-label">Image URL (optional)</label>
          <input
            type="url"
            className="form-control"
            id="imageUrl"
            name="imageUrl"
            value={formData.imageUrl}
            onChange={handleChange}
            placeholder="https://example.com/image.jpg"
          />
        </div>

        <div className="d-flex justify-content-between">
          <button
            type="button"
            className="btn btn-secondary"
            onClick={() => navigate('/recipes')}
          >
            Cancel
          </button>
          <button type="submit" className="btn btn-primary">
            {id ? 'Update Recipe' : 'Add Recipe'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default RecipeForm;
