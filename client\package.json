{"name": "food-recipe-client", "private": true, "version": "1.0.0", "type": "module", "description": "Frontend for Food Recipe App - A modern React application for recipe management", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["food", "recipe", "cooking", "react", "vite", "frontend"], "author": "Food Recipe Team", "license": "MIT", "dependencies": {"axios": "^1.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "bootstrap": "^5.3.1", "react-bootstrap": "^2.8.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "^4.4.5"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}