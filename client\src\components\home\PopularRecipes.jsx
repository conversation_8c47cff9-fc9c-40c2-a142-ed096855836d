import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getRecipes } from '../../services/api';

const PopularRecipes = () => {
  const [recipes, setRecipes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPopularRecipes = async () => {
      try {
        // In a real application, you would have an API endpoint that returns
        // recipes sorted by popularity (views, likes, etc.)
        // For now, we'll just use the existing recipes and simulate popularity
        const data = await getRecipes();

        // Simulate popular recipes by adding a random "views" count
        // and sorting by it (in a real app, this would come from the backend)
        const popularRecipes = data.map(recipe => ({
          ...recipe,
          views: Math.floor(Math.random() * 1000) + 100
        }))
        .sort((a, b) => b.views - a.views)
        .slice(0, 4); // Get top 4

        setRecipes(popularRecipes);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching popular recipes:', err);
        setError('Failed to fetch popular recipes');
        setLoading(false);
      }
    };

    fetchPopularRecipes();
  }, []);

  if (loading) {
    return (
      <div className="container my-5">
        <h2 className="text-center mb-4">Most Popular Recipes</h2>
        <div className="d-flex justify-content-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container my-5">
        <h2 className="text-center mb-4">Most Popular Recipes</h2>
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="container my-4 my-md-5 px-3 px-md-3">
      <div className="row mb-3 mb-md-4">
        <div className="col-12 col-md-8">
          <h2 className="mb-1 mb-md-2">Most Popular Recipes</h2>
          <p className="text-muted small d-none d-md-block">Our community's favorite recipes that everyone loves</p>
        </div>
        <div className="col-12 col-md-4 text-start text-md-end align-self-center mt-2 mt-md-0">
          <Link to="/recipes" className="btn btn-sm btn-outline-primary">
            View All Recipes
          </Link>
        </div>
      </div>

      <div className="row">
        {recipes.length > 0 ? (
          recipes.map((recipe, index) => (
            <div className="col-6 col-md-6 col-lg-3 mb-3 mb-md-4" key={recipe._id}>
              <div className="card h-100 shadow-sm">
                <div className="position-relative">
                  <img
                    src={recipe.imageUrl || 'https://via.placeholder.com/300x200?text=No+Image'}
                    className="card-img-top"
                    alt={recipe.title}
                    style={{ height: '140px', objectFit: 'cover' }}
                  />
                  <div
                    className="position-absolute top-0 start-0 m-2 px-2 py-1 rounded"
                    style={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white', fontSize: '0.7rem' }}
                  >
                    <i className="bi bi-eye me-1"></i>
                    {recipe.views}
                  </div>
                  <div
                    className="position-absolute top-0 end-0 m-2 d-flex align-items-center justify-content-center rounded-circle"
                    style={{
                      width: '28px',
                      height: '28px',
                      backgroundColor: index === 0 ? '#FFD700' : (index === 1 ? '#C0C0C0' : (index === 2 ? '#CD7F32' : 'white')),
                      color: index <= 2 ? 'white' : '#333',
                      fontWeight: 'bold',
                      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                      fontSize: '0.8rem'
                    }}
                  >
                    #{index + 1}
                  </div>
                </div>
                <div className="card-body p-2 p-md-3">
                  <h5 className="card-title h6 mb-1">{recipe.title}</h5>
                  <p className="card-text text-muted mb-1 d-none d-md-block">
                    <small>
                      <i className="bi bi-clock me-1"></i> {recipe.cookingTime} mins |
                      <i className="bi bi-people ms-2 me-1"></i> {recipe.servings}
                    </small>
                  </p>
                  <div className="d-md-none mb-2">
                    <small className="text-muted">
                      <i className="bi bi-clock me-1"></i> {recipe.cookingTime}m
                    </small>
                  </div>
                  <p className="card-text small d-none d-md-block">{recipe.description.substring(0, 60)}...</p>
                  <Link to={`/recipes/${recipe._id}`} className="btn btn-sm btn-primary w-100 mt-1">
                    View Recipe
                  </Link>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-12 text-center">
            <p>No popular recipes found.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PopularRecipes;
