const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    // Get MongoDB connection string from environment variables
    const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/food-recipe-app';

    console.log(`Attempting to connect to MongoDB at: ${MONGO_URI.split('@').length > 1 ? MONGO_URI.split('@')[1] : MONGO_URI}`);

    // Set connection options
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
      connectTimeoutMS: 10000, // Give up initial connection after 10s
    };

    const conn = await mongoose.connect(MONGO_URI, options);

    console.log(`MongoDB Connected: ${conn.connection.host}`);
    console.log(`Database Name: ${conn.connection.name}`);
    return conn;
  } catch (error) {
    console.error(`Error connecting to MongoDB: ${error.message}`);

    // Provide more helpful error messages based on common issues
    if (error.name === 'MongoServerSelectionError') {
      console.error('Could not connect to any MongoDB server.');
      console.error('Please check that MongoDB is installed and running on your machine.');
      console.error('You can start MongoDB with: mongod --dbpath=./data');
    }

    if (error.message.includes('ECONNREFUSED')) {
      console.error('Connection refused. MongoDB may not be running.');
    }

    // Don't exit the process, just log the error and return null
    console.log('Using mock data as MongoDB connection failed');
    return null;
  }
};

module.exports = connectDB;
