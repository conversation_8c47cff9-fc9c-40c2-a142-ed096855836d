const jwt = require('jsonwebtoken');
const User = require('../models/User');
const mongoose = require('mongoose');

const protect = async (req, res, next) => {
  let token;

  // Check if token exists in headers
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    try {
      // Get token from header
      token = req.headers.authorization.split(' ')[1];

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret');

      // Check if MongoDB is connected
      const isConnected = mongoose.connection.readyState === 1;

      if (isConnected) {
        // Get user from database if MongoDB is connected
        req.user = await User.findById(decoded.id).select('-password');
      } else {
        // If MongoDB is not connected, create a mock user from the token
        req.user = {
          _id: decoded.id,
          // We don't have username and email in the token, so these will be undefined
          // The controller methods will handle this
        };
      }

      next();
    } catch (error) {
      console.error('Error authenticating token:', error);
      res.status(401).json({ message: 'Not authorized, token failed' });
    }
  } else if (!token) {
    res.status(401).json({ message: 'Not authorized, no token' });
  }
};

module.exports = { protect };
