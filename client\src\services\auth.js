import axios from 'axios';
import { handleApiError } from '../utils/errorHandler';

// Use environment variable if available, otherwise default to localhost:5001
const API_URL = import.meta.env.VITE_API_URL ? `${import.meta.env.VITE_API_URL}/auth` : 'http://localhost:5001/api/auth';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add a request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const user = JSON.parse(localStorage.getItem('user'));
    if (user && user.token) {
      config.headers.Authorization = `Bearer ${user.token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Register user
export const register = async (userData) => {
  try {
    const response = await api.post('/register', userData);

    if (response.data) {
      localStorage.setItem('user', JSON.stringify(response.data));
    }

    return response.data;
  } catch (error) {
    console.error('Error registering user:', error);
    throw error;
  }
};

// Login user
export const login = async (userData) => {
  try {
    const response = await api.post('/login', userData);

    if (response.data) {
      localStorage.setItem('user', JSON.stringify(response.data));
    }

    return response.data;
  } catch (error) {
    console.error('Error logging in:', error);
    const errorMessage = handleApiError(error, 'Failed to log in');
    throw new Error(errorMessage);
  }
};

// Logout user
export const logout = () => {
  localStorage.removeItem('user');
};

// Get user profile
export const getUserProfile = async () => {
  try {
    const user = JSON.parse(localStorage.getItem('user'));

    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await api.get('/profile');
    return response.data;
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

// Update user profile
export const updateUserProfile = async (userData) => {
  try {
    const user = JSON.parse(localStorage.getItem('user'));

    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await api.put('/profile', userData);

    if (response.data) {
      localStorage.setItem('user', JSON.stringify(response.data));
    }

    return response.data;
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

// Add recipe to favorites
export const addToFavorites = async (recipeId) => {
  try {
    const user = JSON.parse(localStorage.getItem('user'));

    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await api.post(`/favorites/${recipeId}`, {});
    return response.data;
  } catch (error) {
    console.error('Error adding to favorites:', error);
    throw error;
  }
};

// Remove recipe from favorites
export const removeFromFavorites = async (recipeId) => {
  try {
    const user = JSON.parse(localStorage.getItem('user'));

    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await api.delete(`/favorites/${recipeId}`);
    return response.data;
  } catch (error) {
    console.error('Error removing from favorites:', error);
    throw error;
  }
};

// Get user favorites
export const getFavorites = async () => {
  try {
    const user = JSON.parse(localStorage.getItem('user'));

    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await api.get('/favorites');
    return response.data;
  } catch (error) {
    console.error('Error getting favorites:', error);
    throw error;
  }
};

// Get current user from localStorage
export const getCurrentUser = () => {
  return JSON.parse(localStorage.getItem('user'));
};

// Check if user is authenticated
export const isAuthenticated = () => {
  return localStorage.getItem('user') !== null;
};
