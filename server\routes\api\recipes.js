const express = require('express');
const router = express.Router();
const {
  getRecipes,
  getRecipeById,
  createRecipe,
  updateRecipe,
  deleteRecipe
} = require('../../controllers/recipeController');

// @route   GET & POST /api/recipes
router.route('/')
  .get(getRecipes)
  .post(createRecipe);

// @route   GET & PUT & DELETE /api/recipes/:id
router.route('/:id')
  .get(getRecipeById)
  .put(updateRecipe)
  .delete(deleteRecipe);

module.exports = router;
