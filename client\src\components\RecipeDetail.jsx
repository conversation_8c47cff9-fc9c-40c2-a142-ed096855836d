import { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { getRecipeById, deleteRecipe } from '../services/api';
import { isAuthenticated, addToFavorites, removeFromFavorites, getFavorites } from '../services/auth';
import { getMealById, convertMealToRecipe } from '../services/mealdb';

const RecipeDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [recipe, setRecipe] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [authenticated, setAuthenticated] = useState(false);
  const [isExternalRecipe, setIsExternalRecipe] = useState(false);
  const [activeTab, setActiveTab] = useState('ingredients');

  useEffect(() => {
    setAuthenticated(isAuthenticated());

    const fetchRecipe = async () => {
      try {
        // First try to fetch from TheMealDB
        try {
          const mealData = await getMealById(id);
          if (mealData) {
            const formattedRecipe = convertMealToRecipe(mealData);
            setRecipe(formattedRecipe);
            setIsExternalRecipe(true);
            setLoading(false);
            return;
          }
        } catch (mealErr) {
          console.log('Not a TheMealDB recipe, trying local API');
        }

        // If not found in TheMealDB, try local API
        const data = await getRecipeById(id);
        setRecipe(data);
        setIsExternalRecipe(false);
        setLoading(false);

        // Check if recipe is in favorites
        if (isAuthenticated()) {
          try {
            const favorites = await getFavorites();
            const isInFavorites = favorites.some(fav => fav._id === id);
            setIsFavorite(isInFavorites);
          } catch (err) {
            console.error('Error checking favorites:', err);
          }
        }
      } catch (err) {
        setError('Failed to fetch recipe details');
        setLoading(false);
      }
    };

    fetchRecipe();
  }, [id]);

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this recipe?')) {
      try {
        await deleteRecipe(id);
        navigate('/recipes');
      } catch (err) {
        setError('Failed to delete recipe');
      }
    }
  };

  const handleToggleFavorite = async () => {
    if (!authenticated) {
      navigate('/login');
      return;
    }

    try {
      if (isFavorite) {
        await removeFromFavorites(id);
        setIsFavorite(false);
      } else {
        await addToFavorites(id);
        setIsFavorite(true);
      }
    } catch (err) {
      setError('Failed to update favorites');
    }
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy': return 'success';
      case 'medium': return 'warning';
      case 'hard': return 'danger';
      default: return 'secondary';
    }
  };

  const formatTime = (minutes) => {
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${minutes}m`;
  };

  if (loading) {
    return (
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-lg-8">
            <div className="text-center py-5">
              <div className="spinner-border text-primary mb-3" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <h4 className="text-muted">Loading recipe...</h4>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error && !recipe) {
    return (
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-lg-8">
            <div className="alert alert-danger text-center">
              <h4>Recipe Not Found</h4>
              <p>{error}</p>
              <Link to="/recipes" className="btn btn-primary">
                <i className="bi bi-arrow-left me-2"></i>
                Back to Recipes
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!recipe) {
    return (
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-lg-8">
            <div className="alert alert-warning text-center">
              <h4>Recipe Not Found</h4>
              <p>The recipe you're looking for doesn't exist.</p>
              <Link to="/recipes" className="btn btn-primary">
                <i className="bi bi-arrow-left me-2"></i>
                Back to Recipes
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="recipe-detail-page bg-light min-vh-100">
      <div className="container py-4">
        {/* Breadcrumb */}
        <nav aria-label="breadcrumb" className="mb-4">
          <ol className="breadcrumb">
            <li className="breadcrumb-item">
              <Link to="/" className="text-decoration-none">Home</Link>
            </li>
            <li className="breadcrumb-item">
              <Link to="/recipes" className="text-decoration-none">Recipes</Link>
            </li>
            <li className="breadcrumb-item active" aria-current="page">
              {recipe.title}
            </li>
          </ol>
        </nav>

        <div className="row g-4">
          {/* Recipe Image and Basic Info */}
          <div className="col-lg-5">
            <div className="recipe-image-card bg-white rounded-4 overflow-hidden shadow-sm mb-4">
              <div className="position-relative">
                <img
                  src={recipe.imageUrl || 'https://via.placeholder.com/600x400?text=Recipe'}
                  alt={recipe.title}
                  className="w-100"
                  style={{ height: '400px', objectFit: 'cover' }}
                />

                {/* Rating Badge */}
                {recipe.rating && (
                  <div className="position-absolute top-0 end-0 m-3">
                    <span className="badge bg-dark bg-opacity-75 text-white px-3 py-2 rounded-pill fs-6">
                      <i className="bi bi-star-fill text-warning me-1"></i>
                      {recipe.rating}
                    </span>
                  </div>
                )}

                {/* Difficulty Badge */}
                <div className="position-absolute top-0 start-0 m-3">
                  <span className={`badge bg-${getDifficultyColor(recipe.difficulty)} px-3 py-2 rounded-pill fs-6`}>
                    {recipe.difficulty}
                  </span>
                </div>
              </div>
            </div>

            {/* Recipe Stats */}
            <div className="recipe-stats bg-white rounded-4 p-4 shadow-sm">
              <h5 className="mb-3 text-center">Recipe Information</h5>
              <div className="row g-3 text-center">
                <div className="col-6">
                  <div className="stat-item">
                    <i className="bi bi-clock text-primary fs-4 mb-2 d-block"></i>
                    <div className="fw-bold">{formatTime(recipe.cookingTime)}</div>
                    <small className="text-muted">Cook Time</small>
                  </div>
                </div>
                <div className="col-6">
                  <div className="stat-item">
                    <i className="bi bi-people text-primary fs-4 mb-2 d-block"></i>
                    <div className="fw-bold">{recipe.servings}</div>
                    <small className="text-muted">Servings</small>
                  </div>
                </div>
                <div className="col-6">
                  <div className="stat-item">
                    <i className="bi bi-bar-chart text-primary fs-4 mb-2 d-block"></i>
                    <div className="fw-bold">{recipe.difficulty}</div>
                    <small className="text-muted">Difficulty</small>
                  </div>
                </div>
                {recipe.category && (
                  <div className="col-6">
                    <div className="stat-item">
                      <i className="bi bi-tag text-primary fs-4 mb-2 d-block"></i>
                      <div className="fw-bold">{recipe.category}</div>
                      <small className="text-muted">Category</small>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Recipe Content */}
          <div className="col-lg-7">
            {/* Recipe Header */}
            <div className="recipe-header bg-white rounded-4 p-4 shadow-sm mb-4">
              <div className="d-flex justify-content-between align-items-start mb-3">
                <div className="flex-grow-1">
                  <h1 className="recipe-title mb-2">{recipe.title}</h1>
                  {recipe.author && (
                    <div className="d-flex align-items-center mb-3">
                      <div className="author-avatar bg-primary rounded-circle d-flex align-items-center justify-content-center me-2"
                           style={{ width: '40px', height: '40px' }}>
                        <i className="bi bi-person text-white"></i>
                      </div>
                      <div>
                        <div className="fw-semibold">by {recipe.author}</div>
                        <small className="text-muted">Recipe Creator</small>
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="d-flex gap-2">
                  {authenticated && (
                    <button
                      onClick={handleToggleFavorite}
                      className={`btn btn-sm ${isFavorite ? 'btn-danger' : 'btn-outline-danger'}`}
                      title={isFavorite ? 'Remove from Favorites' : 'Add to Favorites'}
                    >
                      <i className={`bi ${isFavorite ? 'bi-heart-fill' : 'bi-heart'}`}></i>
                    </button>
                  )}
                  <button className="btn btn-outline-primary btn-sm" title="Share Recipe">
                    <i className="bi bi-share"></i>
                  </button>
                  <div className="dropdown">
                    <button className="btn btn-outline-secondary btn-sm dropdown-toggle"
                            type="button" data-bs-toggle="dropdown">
                      <i className="bi bi-three-dots"></i>
                    </button>
                    <ul className="dropdown-menu">
                      {authenticated && !isExternalRecipe && (
                        <>
                          <li>
                            <Link to={`/edit-recipe/${recipe._id}`} className="dropdown-item">
                              <i className="bi bi-pencil me-2"></i>Edit Recipe
                            </Link>
                          </li>
                          <li><hr className="dropdown-divider" /></li>
                          <li>
                            <button className="dropdown-item text-danger" onClick={handleDelete}>
                              <i className="bi bi-trash me-2"></i>Delete Recipe
                            </button>
                          </li>
                        </>
                      )}
                      {isExternalRecipe && (
                        <li>
                          <a
                            href={recipe.source || `https://www.themealdb.com/meal/${recipe._id}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="dropdown-item"
                          >
                            <i className="bi bi-box-arrow-up-right me-2"></i>View Original Recipe
                          </a>
                        </li>
                      )}
                      <li>
                        <button className="dropdown-item">
                          <i className="bi bi-printer me-2"></i>Print Recipe
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <p className="recipe-description text-muted mb-3">{recipe.description}</p>

              {/* Tags */}
              {recipe.tags && recipe.tags.length > 0 && (
                <div className="recipe-tags">
                  {recipe.tags.map((tag, index) => (
                    <span key={index} className="badge bg-light text-dark me-2 mb-2">
                      #{tag}
                    </span>
                  ))}
                </div>
              )}
            </div>

            {/* Recipe Content Tabs */}
            <div className="recipe-content bg-white rounded-4 shadow-sm">
              {/* Tab Navigation */}
              <div className="border-bottom">
                <nav className="nav nav-tabs border-0 px-4 pt-4">
                  <button
                    className={`nav-link border-0 fw-semibold ${activeTab === 'ingredients' ? 'active text-primary border-bottom border-primary border-2' : 'text-muted'}`}
                    onClick={() => setActiveTab('ingredients')}
                  >
                    <i className="bi bi-list-ul me-2"></i>
                    Ingredients
                  </button>
                  <button
                    className={`nav-link border-0 fw-semibold ${activeTab === 'instructions' ? 'active text-primary border-bottom border-primary border-2' : 'text-muted'}`}
                    onClick={() => setActiveTab('instructions')}
                  >
                    <i className="bi bi-list-ol me-2"></i>
                    Instructions
                  </button>
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-4">
                {activeTab === 'ingredients' && (
                  <div className="ingredients-tab">
                    <h5 className="mb-3">Ingredients ({recipe.ingredients?.length || 0})</h5>
                    <div className="ingredients-list">
                      {recipe.ingredients?.map((ingredient, index) => (
                        <div key={index} className="ingredient-item d-flex align-items-center py-2 border-bottom">
                          <div className="form-check me-3">
                            <input className="form-check-input" type="checkbox" id={`ingredient-${index}`} />
                          </div>
                          <label className="form-check-label flex-grow-1" htmlFor={`ingredient-${index}`}>
                            {ingredient}
                          </label>
                        </div>
                      )) || (
                        <p className="text-muted">No ingredients listed.</p>
                      )}
                    </div>
                  </div>
                )}

                {activeTab === 'instructions' && (
                  <div className="instructions-tab">
                    <h5 className="mb-3">Instructions</h5>
                    <div className="instructions-list">
                      {recipe.instructions ? (
                        typeof recipe.instructions === 'string' ? (
                          recipe.instructions.split('\n').map((instruction, index) => (
                            instruction.trim() && (
                              <div key={index} className="instruction-item d-flex mb-4">
                                <div className="step-number bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3 flex-shrink-0"
                                     style={{ width: '32px', height: '32px', fontSize: '0.875rem' }}>
                                  {index + 1}
                                </div>
                                <div className="step-content">
                                  <p className="mb-0">{instruction.trim()}</p>
                                </div>
                              </div>
                            )
                          ))
                        ) : (
                          recipe.instructions.map((instruction, index) => (
                            <div key={index} className="instruction-item d-flex mb-4">
                              <div className="step-number bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3 flex-shrink-0"
                                   style={{ width: '32px', height: '32px', fontSize: '0.875rem' }}>
                                {index + 1}
                              </div>
                              <div className="step-content">
                                <p className="mb-0">{instruction}</p>
                              </div>
                            </div>
                          ))
                        )
                      ) : (
                        <p className="text-muted">No instructions provided.</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="recipe-actions mt-4 d-flex gap-3 flex-wrap">
              <Link to="/recipes" className="btn btn-outline-primary">
                <i className="bi bi-arrow-left me-2"></i>
                Back to Recipes
              </Link>
              <button className="btn btn-primary" onClick={() => window.print()}>
                <i className="bi bi-printer me-2"></i>
                Print Recipe
              </button>
              {authenticated && (
                <button
                  onClick={handleToggleFavorite}
                  className={`btn ${isFavorite ? 'btn-danger' : 'btn-success'}`}
                >
                  <i className={`bi ${isFavorite ? 'bi-heart-fill' : 'bi-heart'} me-2`}></i>
                  {isFavorite ? 'Remove from Favorites' : 'Save Recipe'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecipeDetail;
