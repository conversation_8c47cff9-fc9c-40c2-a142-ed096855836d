import { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { getRecipeById, deleteRecipe } from '../services/api';
import { isAuthenticated, addToFavorites, removeFromFavorites, getFavorites } from '../services/auth';
import { getMealById, convertMealToRecipe } from '../services/mealdb';

const RecipeDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [recipe, setRecipe] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [authenticated, setAuthenticated] = useState(false);
  const [isExternalRecipe, setIsExternalRecipe] = useState(false);

  useEffect(() => {
    setAuthenticated(isAuthenticated());

    const fetchRecipe = async () => {
      try {
        // First try to fetch from TheMealDB
        try {
          const mealData = await getMealById(id);
          if (mealData) {
            const formattedRecipe = convertMealToRecipe(mealData);
            setRecipe(formattedRecipe);
            setIsExternalRecipe(true);
            setLoading(false);
            return;
          }
        } catch (mealErr) {
          console.log('Not a TheMealDB recipe, trying local API');
        }

        // If not found in TheMealDB, try local API
        const data = await getRecipeById(id);
        setRecipe(data);
        setIsExternalRecipe(false);
        setLoading(false);

        // Check if recipe is in favorites
        if (isAuthenticated()) {
          try {
            const favorites = await getFavorites();
            const isInFavorites = favorites.some(fav => fav._id === id);
            setIsFavorite(isInFavorites);
          } catch (err) {
            console.error('Error checking favorites:', err);
          }
        }
      } catch (err) {
        setError('Failed to fetch recipe details');
        setLoading(false);
      }
    };

    fetchRecipe();
  }, [id]);

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this recipe?')) {
      try {
        await deleteRecipe(id);
        navigate('/recipes');
      } catch (err) {
        setError('Failed to delete recipe');
      }
    }
  };

  const handleToggleFavorite = async () => {
    if (!authenticated) {
      navigate('/login');
      return;
    }

    try {
      if (isFavorite) {
        await removeFromFavorites(id);
        setIsFavorite(false);
      } else {
        await addToFavorites(id);
        setIsFavorite(true);
      }
    } catch (err) {
      setError('Failed to update favorites');
    }
  };

  if (loading) {
    return <div className="text-center mt-5"><div className="spinner-border"></div></div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (!recipe) {
    return <div className="alert alert-warning">Recipe not found</div>;
  }

  return (
    <div className="recipe-detail-container">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>{recipe.title}</h2>
        <div>
          {authenticated && (
            <button
              onClick={handleToggleFavorite}
              className={`btn me-2 ${isFavorite ? 'btn-danger' : 'btn-outline-danger'}`}
            >
              {isFavorite ? 'Remove from Favorites' : 'Add to Favorites'}
            </button>
          )}
          {authenticated && !isExternalRecipe && (
            <>
              <Link to={`/edit-recipe/${recipe._id}`} className="btn btn-primary me-2">
                Edit
              </Link>
              <button onClick={handleDelete} className="btn btn-danger">
                Delete
              </button>
            </>
          )}
          {isExternalRecipe && (
            <a
              href={recipe.source || `https://www.themealdb.com/meal/${recipe._id}`}
              target="_blank"
              rel="noopener noreferrer"
              className="btn btn-info me-2"
            >
              View Original Recipe
            </a>
          )}
        </div>
      </div>

      <div className="recipe-image-container">
        <img
          src={recipe.imageUrl || 'https://via.placeholder.com/800x400?text=No+Image'}
          alt={recipe.title}
          className="img-fluid"
        />
      </div>

      <div className="cooking-info">
        <div className="cooking-info-item">
          <p>{recipe.cookingTime} mins</p>
          <span>Cooking Time</span>
        </div>
        <div className="cooking-info-item">
          <p>{recipe.servings}</p>
          <span>Servings</span>
        </div>
        <div className="cooking-info-item">
          <p>{recipe.difficulty}</p>
          <span>Difficulty</span>
        </div>
      </div>

      <div className="mb-4">
        <h4>Description</h4>
        <p>{recipe.description}</p>
      </div>

      <div className="row">
        <div className="col-md-4 mb-4">
          <h4>Ingredients</h4>
          <ul className="ingredients-list">
            {recipe.ingredients.map((ingredient, index) => (
              <li key={index}>{ingredient}</li>
            ))}
          </ul>
        </div>
        <div className="col-md-8">
          <h4>Instructions</h4>
          <div className="card">
            <div className="card-body">
              {recipe.instructions.split('\n').map((step, index) => (
                <p key={index}>{step}</p>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="mt-4">
        <Link to="/recipes" className="btn btn-secondary">
          Back to Recipes
        </Link>
      </div>
    </div>
  );
};

export default RecipeDetail;
