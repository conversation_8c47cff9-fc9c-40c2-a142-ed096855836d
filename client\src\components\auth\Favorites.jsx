import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { getFavorites, removeFromFavorites } from '../../services/auth';
import { isAuthenticated } from '../../services/auth';

const Favorites = () => {
  const navigate = useNavigate();
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!isAuthenticated()) {
      navigate('/login');
      return;
    }
    
    const fetchFavorites = async () => {
      try {
        const data = await getFavorites();
        setFavorites(data);
        setLoading(false);
      } catch (err) {
        setError('Failed to load favorites');
        setLoading(false);
      }
    };
    
    fetchFavorites();
  }, [navigate]);

  const handleRemoveFromFavorites = async (recipeId) => {
    try {
      await removeFromFavorites(recipeId);
      setFavorites(favorites.filter(recipe => recipe._id !== recipeId));
    } catch (err) {
      setError('Failed to remove from favorites');
    }
  };

  if (loading) {
    return (
      <div className="container mt-5 text-center">
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mt-5">
        <div className="alert alert-danger">{error}</div>
      </div>
    );
  }

  return (
    <div className="container mt-5">
      <h2 className="mb-4">My Favorite Recipes</h2>
      
      {favorites.length === 0 ? (
        <div className="text-center">
          <p>You haven't added any recipes to your favorites yet.</p>
          <Link to="/recipes" className="btn btn-primary">Browse Recipes</Link>
        </div>
      ) : (
        <div className="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
          {favorites.map((recipe) => (
            <div className="col" key={recipe._id}>
              <div className="card h-100 recipe-card">
                <div className="position-relative">
                  <img 
                    src={recipe.imageUrl || 'https://via.placeholder.com/300x200?text=No+Image'} 
                    className="card-img-top recipe-image" 
                    alt={recipe.title} 
                  />
                  <span className={`badge difficulty-badge bg-${
                    recipe.difficulty === 'Easy' ? 'success' : 
                    recipe.difficulty === 'Medium' ? 'warning' : 'danger'
                  }`}>
                    {recipe.difficulty}
                  </span>
                </div>
                <div className="card-body">
                  <h5 className="card-title">{recipe.title}</h5>
                  <p className="card-text">{recipe.description.substring(0, 100)}...</p>
                  <div className="d-flex justify-content-between align-items-center">
                    <small className="text-muted">
                      {recipe.cookingTime} mins | {recipe.servings} servings
                    </small>
                  </div>
                </div>
                <div className="card-footer d-flex justify-content-between">
                  <Link to={`/recipes/${recipe._id}`} className="btn btn-sm btn-primary">
                    View Recipe
                  </Link>
                  <button 
                    className="btn btn-sm btn-outline-danger"
                    onClick={() => handleRemoveFromFavorites(recipe._id)}
                  >
                    Remove
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Favorites;
