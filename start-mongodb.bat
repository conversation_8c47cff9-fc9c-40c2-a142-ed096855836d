@echo off
echo Setting up MongoDB for Food Recipe App...
echo.

REM Create data directory if it doesn't exist
if not exist "data\db" (
    echo Creating MongoDB data directory...
    mkdir data\db
)

echo.
echo Starting MongoDB server...
echo.
echo NOTE: If this fails, please make sure MongoDB is installed on your system.
echo You can download MongoDB Community Edition from: https://www.mongodb.com/try/download/community
echo.
echo After installation, you may need to add MongoDB bin directory to your PATH.
echo.

REM Try to start MongoDB
mongod --dbpath=data\db

REM If MongoDB command fails, provide instructions
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Failed to start MongoDB. Please check if MongoDB is installed.
    echo.
    echo If MongoDB is not installed:
    echo 1. Download MongoDB Community Edition from: https://www.mongodb.com/try/download/community
    echo 2. Follow the installation instructions
    echo 3. Add the MongoDB bin directory to your PATH
    echo.
    echo Alternatively, you can use MongoDB Atlas (cloud service):
    echo 1. Create a free account at: https://www.mongodb.com/cloud/atlas/register
    echo 2. Create a new cluster
    echo 3. Get your connection string and update it in server/.env file
    echo.
    pause
)
