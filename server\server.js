console.log('Starting server...');
const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const connectDB = require('./config/db');
const Recipe = require('./models/Recipe');
const User = require('./models/User');
console.log('Modules loaded...');

// Load environment variables
dotenv.config();

// Initialize express app
const app = express();

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'], // Vite default port and React default port
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));
app.use(express.json());

// Mock data for recipes
const recipes = [
  {
    _id: '1',
    title: 'Spaghetti Carbonara',
    description: 'A classic Italian pasta dish with eggs, cheese, pancetta, and black pepper.',
    ingredients: ['200g spaghetti', '100g pancetta', '2 large eggs', '50g Pecorino Romano cheese', 'Black pepper'],
    instructions: 'Cook pasta. Fry pancetta. Mix eggs and cheese. Combine all ingredients.',
    cookingTime: 20,
    servings: 2,
    difficulty: 'Medium',
    imageUrl: 'https://images.unsplash.com/photo-1612874742237-6526221588e3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1771&q=80',
    createdAt: new Date()
  },
  {
    _id: '2',
    title: 'Chicken Curry',
    description: 'A flavorful Indian curry with tender chicken pieces in a rich sauce.',
    ingredients: ['500g chicken', '1 onion', '2 cloves garlic', '1 tbsp curry powder', 'Coconut milk'],
    instructions: 'Sauté onions and garlic. Add chicken and spices. Pour in coconut milk and simmer.',
    cookingTime: 40,
    servings: 4,
    difficulty: 'Medium',
    imageUrl: 'https://images.unsplash.com/photo-1604952564555-13c872c0266f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80',
    createdAt: new Date()
  }
];

// Connect to MongoDB
let dbConnected = false;
console.log('Attempting to connect to MongoDB...');
console.log('MongoDB URI:', process.env.MONGO_URI ? process.env.MONGO_URI.split('@')[1] : 'Not set');

connectDB().then(conn => {
  if (conn) {
    dbConnected = true;
    console.log('MongoDB connected successfully');
    console.log(`Connected to database: ${conn.connection.name}`);
    console.log(`Connected to host: ${conn.connection.host}`);

    // Seed initial recipes if the database is empty
    Recipe.countDocuments().then(count => {
      console.log(`Found ${count} recipes in database`);
      if (count === 0) {
        console.log('Seeding initial recipes...');
        Recipe.insertMany(recipes)
          .then(() => console.log('Initial recipes seeded'))
          .catch(err => console.error('Error seeding recipes:', err));
      }
    }).catch(err => {
      console.error('Error counting recipes:', err);
    });
  } else {
    console.log('Using mock data as MongoDB connection failed');
  }
}).catch(err => {
  console.error('Error in MongoDB connection promise:', err);
});

// API routes with MongoDB fallback
app.get('/api/recipes', async (req, res) => {
  try {
    const { search, difficulty, minTime, maxTime, minServings, maxServings } = req.query;

    if (dbConnected) {
      let query = {};

      // Search functionality
      if (search) {
        query = {
          $or: [
            { title: { $regex: search, $options: 'i' } },
            { description: { $regex: search, $options: 'i' } },
            { ingredients: { $elemMatch: { $regex: search, $options: 'i' } } }
          ]
        };
      }

      // Filter by difficulty
      if (difficulty) {
        query.difficulty = difficulty;
      }

      // Filter by cooking time range
      if (minTime || maxTime) {
        query.cookingTime = {};
        if (minTime) query.cookingTime.$gte = parseInt(minTime);
        if (maxTime) query.cookingTime.$lte = parseInt(maxTime);
      }

      // Filter by servings range
      if (minServings || maxServings) {
        query.servings = {};
        if (minServings) query.servings.$gte = parseInt(minServings);
        if (maxServings) query.servings.$lte = parseInt(maxServings);
      }

      const dbRecipes = await Recipe.find(query).sort({ createdAt: -1 });
      res.json(dbRecipes);
    } else {
      // If MongoDB is not connected, filter the mock data
      let filteredRecipes = [...recipes];

      // Search functionality
      if (search) {
        const searchLower = search.toLowerCase();
        filteredRecipes = filteredRecipes.filter(recipe =>
          recipe.title.toLowerCase().includes(searchLower) ||
          recipe.description.toLowerCase().includes(searchLower) ||
          recipe.ingredients.some(ing => ing.toLowerCase().includes(searchLower))
        );
      }

      // Filter by difficulty
      if (difficulty) {
        filteredRecipes = filteredRecipes.filter(recipe => recipe.difficulty === difficulty);
      }

      // Filter by cooking time range
      if (minTime) {
        filteredRecipes = filteredRecipes.filter(recipe => recipe.cookingTime >= parseInt(minTime));
      }
      if (maxTime) {
        filteredRecipes = filteredRecipes.filter(recipe => recipe.cookingTime <= parseInt(maxTime));
      }

      // Filter by servings range
      if (minServings) {
        filteredRecipes = filteredRecipes.filter(recipe => recipe.servings >= parseInt(minServings));
      }
      if (maxServings) {
        filteredRecipes = filteredRecipes.filter(recipe => recipe.servings <= parseInt(maxServings));
      }

      res.json(filteredRecipes);
    }
  } catch (error) {
    console.error('Error fetching recipes:', error);
    res.json(recipes); // Fallback to mock data
  }
});

app.get('/api/recipes/:id', async (req, res) => {
  try {
    if (dbConnected) {
      const recipe = await Recipe.findById(req.params.id);
      if (!recipe) {
        const mockRecipe = recipes.find(r => r._id === req.params.id);
        if (mockRecipe) {
          return res.json(mockRecipe);
        }
        return res.status(404).json({ message: 'Recipe not found' });
      }
      res.json(recipe);
    } else {
      const recipe = recipes.find(r => r._id === req.params.id);
      if (!recipe) {
        return res.status(404).json({ message: 'Recipe not found' });
      }
      res.json(recipe);
    }
  } catch (error) {
    console.error('Error fetching recipe:', error);
    const recipe = recipes.find(r => r._id === req.params.id);
    if (!recipe) {
      return res.status(404).json({ message: 'Recipe not found' });
    }
    res.json(recipe);
  }
});

app.post('/api/recipes', async (req, res) => {
  try {
    if (dbConnected) {
      const newRecipe = new Recipe(req.body);
      const savedRecipe = await newRecipe.save();
      res.status(201).json(savedRecipe);
    } else {
      const newRecipe = {
        _id: Date.now().toString(),
        ...req.body,
        createdAt: new Date()
      };
      recipes.push(newRecipe);
      res.status(201).json(newRecipe);
    }
  } catch (error) {
    console.error('Error creating recipe:', error);
    const newRecipe = {
      _id: Date.now().toString(),
      ...req.body,
      createdAt: new Date()
    };
    recipes.push(newRecipe);
    res.status(201).json(newRecipe);
  }
});

app.put('/api/recipes/:id', async (req, res) => {
  try {
    if (dbConnected) {
      const updatedRecipe = await Recipe.findByIdAndUpdate(
        req.params.id,
        req.body,
        { new: true, runValidators: true }
      );
      if (!updatedRecipe) {
        const index = recipes.findIndex(r => r._id === req.params.id);
        if (index === -1) {
          return res.status(404).json({ message: 'Recipe not found' });
        }
        recipes[index] = { ...recipes[index], ...req.body };
        return res.json(recipes[index]);
      }
      res.json(updatedRecipe);
    } else {
      const index = recipes.findIndex(r => r._id === req.params.id);
      if (index === -1) {
        return res.status(404).json({ message: 'Recipe not found' });
      }
      recipes[index] = { ...recipes[index], ...req.body };
      res.json(recipes[index]);
    }
  } catch (error) {
    console.error('Error updating recipe:', error);
    const index = recipes.findIndex(r => r._id === req.params.id);
    if (index === -1) {
      return res.status(404).json({ message: 'Recipe not found' });
    }
    recipes[index] = { ...recipes[index], ...req.body };
    res.json(recipes[index]);
  }
});

app.delete('/api/recipes/:id', async (req, res) => {
  try {
    if (dbConnected) {
      const deletedRecipe = await Recipe.findByIdAndDelete(req.params.id);
      if (!deletedRecipe) {
        const index = recipes.findIndex(r => r._id === req.params.id);
        if (index === -1) {
          return res.status(404).json({ message: 'Recipe not found' });
        }
        recipes.splice(index, 1);
        return res.json({ message: 'Recipe removed' });
      }
      res.json({ message: 'Recipe removed' });
    } else {
      const index = recipes.findIndex(r => r._id === req.params.id);
      if (index === -1) {
        return res.status(404).json({ message: 'Recipe not found' });
      }
      recipes.splice(index, 1);
      res.json({ message: 'Recipe removed' });
    }
  } catch (error) {
    console.error('Error deleting recipe:', error);
    const index = recipes.findIndex(r => r._id === req.params.id);
    if (index === -1) {
      return res.status(404).json({ message: 'Recipe not found' });
    }
    recipes.splice(index, 1);
    res.json({ message: 'Recipe removed' });
  }
});

// Auth routes
app.use('/api/auth', require('./routes/api/auth'));

// Default route
app.get('/', (req, res) => {
  res.send('Food Recipe API is running...');
});

// Set port
const PORT = process.env.PORT || 5001;

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
