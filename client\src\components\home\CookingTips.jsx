import { Link } from 'react-router-dom';

const CookingTips = () => {
  const tips = [
    {
      id: 1,
      title: 'Master the Art of Knife Skills',
      content: 'Learn proper knife techniques to improve your cooking efficiency and food presentation. Start with the basic cuts: dice, julienne, and chiffonade.',
      image: 'https://images.unsplash.com/photo-1570168007204-dfb528c6958f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80',
      icon: 'bi-knife'
    },
    {
      id: 2,
      title: 'The Secret to Perfect Pasta',
      content: 'Always salt your pasta water generously and save some pasta water before draining. The starchy water helps sauce cling to the pasta and adds silkiness.',
      image: 'https://images.unsplash.com/photo-1551183053-bf91a1d81141?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80',
      icon: 'bi-cup-hot'
    },
    {
      id: 3,
      title: 'Meal Prep Like a Pro',
      content: 'Spend a few hours on Sunday preparing ingredients for the week. Chop vegetables, cook grains, and marinate proteins to save time on busy weeknights.',
      image: 'https://images.unsplash.com/photo-1498837167922-ddd27525d352?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80',
      icon: 'bi-calendar-check'
    }
  ];

  return (
    <div className="cooking-tips py-4 py-md-5 bg-light">
      <div className="container px-3 px-md-3">
        <div className="row mb-3 mb-md-4">
          <div className="col-12 text-center">
            <h2 className="mb-2 mb-md-3">Cooking Tips & Tricks</h2>
            <p className="text-muted small">
              Enhance your culinary skills with these expert tips and tricks
            </p>
          </div>
        </div>

        <div className="row">
          {tips.map((tip) => (
            <div className="col-12 col-md-4 mb-3 mb-md-4" key={tip.id}>
              <div className="card h-100 border-0 shadow-sm tip-card">
                <div className="row g-0 flex-row flex-md-column">
                  <div className="col-4 col-md-12 position-relative">
                    <img
                      src={tip.image}
                      className="img-fluid rounded-start rounded-md-0 rounded-md-top w-100"
                      alt={tip.title}
                      style={{ height: '100%', maxHeight: '200px', objectFit: 'cover' }}
                    />
                    <div
                      className="position-absolute top-0 start-0 m-2 m-md-3 rounded-circle d-flex align-items-center justify-content-center"
                      style={{
                        width: '40px',
                        height: '40px',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                      }}
                    >
                      <i className={`bi ${tip.icon} fs-5 text-primary`}></i>
                    </div>
                  </div>
                  <div className="col-8 col-md-12">
                    <div className="card-body p-3">
                      <h5 className="card-title h6 h5-md">{tip.title}</h5>
                      <p className="card-text text-muted small">{tip.content}</p>
                      <Link to="#" className="btn btn-link text-primary p-0 small">
                        Read more <i className="bi bi-arrow-right"></i>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="row mt-2 mt-md-3">
          <div className="col-12 text-center">
            <Link to="#" className="btn btn-outline-primary">
              View All Cooking Tips
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookingTips;
