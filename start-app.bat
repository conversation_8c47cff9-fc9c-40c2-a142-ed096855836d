@echo off
echo Starting Food Recipe Application...
echo.

echo IMPORTANT: Make sure MongoDB is running before starting the application.
echo If MongoDB is not running, the app will use mock data instead.
echo You can start MongoDB by running the start-mongodb.bat script.
echo.

echo Starting Backend Server...
start cmd /k "cd server && npm run dev"

echo Starting Frontend Server...
start cmd /k "cd client && npm run dev"

echo.
echo Servers are starting. Please wait a moment...
echo.
echo Backend will be available at: http://localhost:5001
echo Frontend will be available at: http://localhost:5173
echo.
echo To test the connection between frontend and backend:
echo 1. Open http://localhost:5173/test-connection in your browser
echo.
echo Press any key to exit this window (servers will continue running)
pause > nul
