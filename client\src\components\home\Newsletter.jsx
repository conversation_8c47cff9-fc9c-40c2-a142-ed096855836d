import { useState } from 'react';

const Newsletter = () => {
  const [email, setEmail] = useState('');
  const [subscribed, setSubscribed] = useState(false);
  const [error, setError] = useState(null);

  const handleSubmit = (e) => {
    e.preventDefault();

    // Simple email validation
    if (!email || !email.includes('@') || !email.includes('.')) {
      setError('Please enter a valid email address');
      return;
    }

    // In a real application, you would send this to your backend
    console.log('Subscribing email:', email);

    // Simulate successful subscription
    setSubscribed(true);
    setError(null);
    setEmail('');

    // Reset the success message after 5 seconds
    setTimeout(() => {
      setSubscribed(false);
    }, 5000);
  };

  return (
    <div className="bg-light py-4 py-md-5">
      <div className="container px-3 px-md-3">
        <div className="row justify-content-center">
          <div className="col-12 col-md-10 col-lg-8 text-center">
            <h2 className="mb-2 mb-md-3">Subscribe to Our Newsletter</h2>
            <p className="text-muted mb-3 mb-md-4 small">
              Get weekly recipe inspiration, cooking tips, and exclusive offers delivered to your inbox.
            </p>

            {subscribed && (
              <div className="alert alert-success py-2 mb-3" role="alert">
                Thank you for subscribing! You'll receive our next newsletter soon.
              </div>
            )}

            {error && (
              <div className="alert alert-danger py-2 mb-3" role="alert">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit} className="mb-2">
              <div className="row g-2 justify-content-center">
                <div className="col-12 col-md-8 mb-2 mb-md-0">
                  <input
                    type="email"
                    className="form-control"
                    placeholder="Your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
                <div className="col-12 col-md-auto">
                  <button type="submit" className="btn btn-primary w-100">
                    Subscribe
                  </button>
                </div>
              </div>
            </form>

            <p className="mt-2 small text-muted">
              We respect your privacy. Unsubscribe at any time.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Newsletter;
