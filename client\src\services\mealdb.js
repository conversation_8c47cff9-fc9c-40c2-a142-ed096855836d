import axios from 'axios';

const BASE_URL = 'https://www.themealdb.com/api/json/v1/1';

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL
});

// Search meals by name
export const searchMealsByName = async (name) => {
  try {
    const response = await api.get(`/search.php?s=${encodeURIComponent(name)}`);
    return response.data.meals || [];
  } catch (error) {
    console.error('Error searching meals:', error);
    throw error;
  }
};

// Get meal by ID
export const getMealById = async (id) => {
  try {
    const response = await api.get(`/lookup.php?i=${id}`);
    return response.data.meals ? response.data.meals[0] : null;
  } catch (error) {
    console.error(`Error fetching meal with id ${id}:`, error);
    throw error;
  }
};

// Get random meal
export const getRandomMeal = async () => {
  try {
    const response = await api.get('/random.php');
    return response.data.meals ? response.data.meals[0] : null;
  } catch (error) {
    console.error('Error fetching random meal:', error);
    throw error;
  }
};

// List all categories
export const getCategories = async () => {
  try {
    const response = await api.get('/categories.php');
    return response.data.categories || [];
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }
};

// Filter by category
export const filterByCategory = async (category) => {
  try {
    const response = await api.get(`/filter.php?c=${encodeURIComponent(category)}`);
    return response.data.meals || [];
  } catch (error) {
    console.error(`Error filtering by category ${category}:`, error);
    throw error;
  }
};

// Filter by area (cuisine)
export const filterByArea = async (area) => {
  try {
    const response = await api.get(`/filter.php?a=${encodeURIComponent(area)}`);
    return response.data.meals || [];
  } catch (error) {
    console.error(`Error filtering by area ${area}:`, error);
    throw error;
  }
};

// List all areas (cuisines)
export const getAreas = async () => {
  try {
    const response = await api.get('/list.php?a=list');
    return response.data.meals || [];
  } catch (error) {
    console.error('Error fetching areas:', error);
    throw error;
  }
};

// Convert TheMealDB meal to our app's recipe format
export const convertMealToRecipe = (meal) => {
  // Extract ingredients and measures
  const ingredients = [];
  for (let i = 1; i <= 20; i++) {
    const ingredient = meal[`strIngredient${i}`];
    const measure = meal[`strMeasure${i}`];
    
    if (ingredient && ingredient.trim() !== '') {
      ingredients.push(`${measure ? measure.trim() + ' ' : ''}${ingredient.trim()}`);
    }
  }
  
  // Determine difficulty based on number of ingredients
  let difficulty = 'Medium';
  if (ingredients.length <= 5) {
    difficulty = 'Easy';
  } else if (ingredients.length >= 10) {
    difficulty = 'Hard';
  }
  
  return {
    _id: meal.idMeal,
    title: meal.strMeal,
    description: meal.strTags ? meal.strTags.split(',').join(', ') : 'Delicious recipe',
    ingredients,
    instructions: meal.strInstructions,
    cookingTime: 30, // Default cooking time as TheMealDB doesn't provide this
    servings: 4, // Default servings as TheMealDB doesn't provide this
    difficulty,
    imageUrl: meal.strMealThumb,
    category: meal.strCategory,
    area: meal.strArea,
    createdAt: new Date()
  };
};
