import { Link } from 'react-router-dom';

const Categories = () => {
  const categories = [
    {
      id: 1,
      name: 'Breakfast',
      image: 'https://images.unsplash.com/photo-1533089860892-a7c6f0a88666?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80',
      description: 'Start your day right with our delicious breakfast recipes'
    },
    {
      id: 2,
      name: 'Main Dishes',
      image: 'https://images.unsplash.com/photo-1547592180-85f173990554?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80',
      description: 'Hearty and satisfying main course recipes for lunch or dinner'
    },
    {
      id: 3,
      name: 'Desser<PERSON>',
      image: 'https://images.unsplash.com/photo-1563729784474-d77dbb933a9e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80',
      description: 'Sweet treats to satisfy your cravings'
    },
    {
      id: 4,
      name: 'Vegetarian',
      image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80',
      description: 'Delicious meat-free recipes packed with flavor'
    }
  ];

  return (
    <div className="container my-4 my-md-5 px-3 px-md-3">
      <h2 className="text-center mb-3 mb-md-4">Browse by Category</h2>
      <div className="row">
        {categories.map((category) => (
          <div className="col-6 col-md-6 col-lg-3 mb-3 mb-md-4" key={category.id}>
            <div className="card h-100 shadow-sm">
              <img
                src={category.image}
                className="card-img-top"
                alt={category.name}
                style={{ height: '140px', objectFit: 'cover' }}
              />
              <div className="card-body text-center p-2 p-md-3">
                <h5 className="card-title h6 h5-md mb-1 mb-md-2">{category.name}</h5>
                <p className="card-text small d-none d-md-block">{category.description}</p>
                <Link
                  to={`/recipes?category=${category.name.toLowerCase()}`}
                  className="btn btn-sm btn-outline-primary mt-1 mt-md-2 w-100"
                >
                  View Recipes
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Categories;
