import { Link } from 'react-router-dom';

const Categories = () => {
  const categories = [
    {
      id: 1,
      name: 'Breakfast',
      description: 'Start your day with energy',
      image: 'https://images.unsplash.com/photo-1533089860892-a7c6f0a88666?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      count: 145,
      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      icon: 'bi-sunrise'
    },
    {
      id: 2,
      name: 'Lunch',
      description: 'Perfect midday meals',
      image: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      count: 267,
      color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      icon: 'bi-sun'
    },
    {
      id: 3,
      name: 'Dinner',
      description: 'Elegant evening dining',
      image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      count: 389,
      color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      icon: 'bi-moon-stars'
    },
    {
      id: 4,
      name: 'Desserts',
      description: 'Sweet indulgences',
      image: 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      count: 134,
      color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      icon: 'bi-heart'
    },
    {
      id: 5,
      name: 'Appetizers',
      description: 'Perfect starters',
      image: 'https://images.unsplash.com/photo-1541014741259-de529411b96a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      count: 98,
      color: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
      icon: 'bi-cup-straw'
    },
    {
      id: 6,
      name: 'Beverages',
      description: 'Refreshing drinks',
      image: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      count: 76,
      color: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
      icon: 'bi-cup-hot'
    }
  ];

  return (
    <section className="categories-section py-5 bg-gradient-light">
      <div className="container">
        <div className="text-center mb-5">
          <span className="badge bg-primary-soft text-primary px-3 py-2 rounded-pill mb-3">
            Explore Categories
          </span>
          <h2 className="display-4 fw-bold text-dark mb-3">
            Browse by <span className="text-primary">Category</span>
          </h2>
          <p className="lead text-muted mx-auto" style={{ maxWidth: '600px' }}>
            Discover delicious recipes organized by meal type and occasion. From hearty breakfasts to decadent desserts.
          </p>
        </div>

        <div className="row g-4">
          {categories.map((category, index) => (
            <div key={category.id} className="col-12 col-sm-6 col-lg-4">
              <Link
                to={`/recipes?category=${category.name.toLowerCase()}`}
                className="text-decoration-none category-link"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="category-card h-100 position-relative overflow-hidden rounded-4 shadow-hover">
                  {/* Background Image */}
                  <div
                    className="category-image position-relative"
                    style={{
                      backgroundImage: `url(${category.image})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      height: '280px'
                    }}
                  >
                    {/* Gradient Overlay */}
                    <div
                      className="category-overlay position-absolute top-0 start-0 w-100 h-100"
                      style={{
                        background: `linear-gradient(45deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, transparent 100%)`
                      }}
                    ></div>

                    {/* Content */}
                    <div className="category-content position-absolute bottom-0 start-0 p-4 text-white">
                      <div className="d-flex align-items-center mb-2">
                        <div
                          className="category-icon rounded-circle d-flex align-items-center justify-content-center me-3"
                          style={{
                            width: '50px',
                            height: '50px',
                            background: category.color,
                            backdropFilter: 'blur(10px)'
                          }}
                        >
                          <i className={`${category.icon} fs-5 text-white`}></i>
                        </div>
                        <div>
                          <h4 className="fw-bold mb-0 text-shadow">{category.name}</h4>
                          <p className="small mb-0 text-white-75">{category.description}</p>
                        </div>
                      </div>

                      <div className="category-stats d-flex align-items-center">
                        <span className="badge bg-white bg-opacity-20 text-white px-3 py-2 rounded-pill">
                          <i className="bi bi-collection me-1"></i>
                          {category.count} recipes
                        </span>
                      </div>
                    </div>

                    {/* Hover Effect */}
                    <div className="category-hover-effect position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
                      <div className="text-center text-white">
                        <i className="bi bi-arrow-right-circle fs-1 mb-2"></i>
                        <p className="fw-semibold">Explore {category.name}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>

        {/* View All Categories Button */}
        <div className="text-center mt-5">
          <Link to="/recipes" className="btn btn-outline-primary btn-lg px-5 py-3 rounded-pill">
            <i className="bi bi-grid-3x3-gap me-2"></i>
            View All Recipes
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Categories;
