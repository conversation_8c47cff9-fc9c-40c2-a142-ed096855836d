import { useState, useEffect } from 'react';

const Testimonials = () => {
  const [activeIndex, setActiveIndex] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      role: '<PERSON> Cook',
      image: 'https://randomuser.me/api/portraits/women/32.jpg',
      text: 'This website has transformed my cooking! The recipes are easy to follow and always turn out delicious. My family now looks forward to dinner time every day.'
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'Food Blogger',
      image: 'https://randomuser.me/api/portraits/men/44.jpg',
      text: 'As a food blogger, I\'m always looking for new recipe ideas. This platform has been an incredible resource for inspiration and discovering unique dishes from around the world.'
    },
    {
      id: 3,
      name: '<PERSON>',
      role: 'Culinary Student',
      image: 'https://randomuser.me/api/portraits/women/68.jpg',
      text: 'The detailed instructions and cooking tips have helped me improve my skills tremendously. I\'ve learned techniques here that weren\'t even covered in my culinary classes!'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [testimonials.length]);

  return (
    <div className="container my-4 my-md-5 px-3 px-md-3">
      <h2 className="text-center mb-3 mb-md-4">What Our Users Say</h2>

      <div id="testimonialCarousel" className="carousel slide" data-bs-ride="carousel">
        <div className="carousel-inner">
          {testimonials.map((testimonial, index) => (
            <div
              key={testimonial.id}
              className={`carousel-item ${index === activeIndex ? 'active' : ''}`}
            >
              <div className="row justify-content-center">
                <div className="col-12 col-lg-10 col-xl-8">
                  <div className="card border-0 shadow-sm">
                    <div className="card-body p-3 p-md-4 p-lg-5">
                      <div className="row align-items-center">
                        <div className="col-12 col-md-4 text-center mb-3 mb-md-0">
                          <img
                            src={testimonial.image}
                            alt={testimonial.name}
                            className="rounded-circle mb-2"
                            width="80"
                            height="80"
                          />
                          <h5 className="mb-0 h6">{testimonial.name}</h5>
                          <p className="text-muted small">{testimonial.role}</p>
                        </div>
                        <div className="col-12 col-md-8">
                          <div className="position-relative">
                            <i className="bi bi-quote fs-1 text-primary opacity-25 position-absolute top-0 start-0 translate-middle"></i>
                            <p className="mb-0 ps-3 ps-md-4 testimonial-text">"{testimonial.text}"</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="d-flex justify-content-center mt-3 mt-md-4">
          {testimonials.map((_, index) => (
            <button
              key={index}
              type="button"
              className={`btn btn-sm ${index === activeIndex ? 'btn-primary' : 'btn-outline-primary'} mx-1`}
              onClick={() => setActiveIndex(index)}
              aria-label={`Testimonial ${index + 1}`}
            >
              {index + 1}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Testimonials;
