import { useState } from 'react';

const SocialShare = () => {
  const [email, setEmail] = useState('');
  const [shared, setShared] = useState(false);

  const handleShare = (platform) => {
    // In a real application, this would open a share dialog for the specific platform
    console.log(`Sharing on ${platform}`);

    // For demonstration purposes, we'll just show a success message
    setShared(true);

    // Reset the success message after 3 seconds
    setTimeout(() => {
      setShared(false);
    }, 3000);
  };

  const handleEmailShare = (e) => {
    e.preventDefault();

    if (email) {
      // In a real application, this would send an email invitation
      console.log(`Sharing via email to: ${email}`);
      setShared(true);
      setEmail('');

      // Reset the success message after 3 seconds
      setTimeout(() => {
        setShared(false);
      }, 3000);
    }
  };

  return (
    <div className="social-share py-4 py-md-5" style={{ backgroundColor: '#f8f9fa' }}>
      <div className="container px-3 px-md-3">
        <div className="row justify-content-center">
          <div className="col-12 col-lg-10 col-xl-8 text-center">
            <h2 className="mb-2 mb-md-3">Share Your Love for Food</h2>
            <p className="text-muted mb-3 mb-md-4 small">
              Connect with friends and family who share your passion for cooking
            </p>

            {shared && (
              <div className="alert alert-success mb-3 mb-md-4 py-2" role="alert">
                Thanks for sharing! Spread the love for good food.
              </div>
            )}

            <div className="social-buttons mb-4 mb-md-5">
              <div className="d-flex flex-wrap justify-content-center gap-2 gap-md-3">
                <button
                  className="btn btn-outline-primary rounded-circle"
                  onClick={() => handleShare('facebook')}
                  style={{ width: '45px', height: '45px' }}
                  aria-label="Share on Facebook"
                >
                  <i className="bi bi-facebook"></i>
                </button>
                <button
                  className="btn btn-outline-info rounded-circle"
                  onClick={() => handleShare('twitter')}
                  style={{ width: '45px', height: '45px' }}
                  aria-label="Share on Twitter"
                >
                  <i className="bi bi-twitter"></i>
                </button>
                <button
                  className="btn btn-outline-danger rounded-circle"
                  onClick={() => handleShare('pinterest')}
                  style={{ width: '45px', height: '45px' }}
                  aria-label="Share on Pinterest"
                >
                  <i className="bi bi-pinterest"></i>
                </button>
                <button
                  className="btn btn-outline-success rounded-circle"
                  onClick={() => handleShare('whatsapp')}
                  style={{ width: '45px', height: '45px' }}
                  aria-label="Share on WhatsApp"
                >
                  <i className="bi bi-whatsapp"></i>
                </button>
                <button
                  className="btn btn-outline-secondary rounded-circle"
                  onClick={() => handleShare('email')}
                  style={{ width: '45px', height: '45px' }}
                  aria-label="Share via Email"
                >
                  <i className="bi bi-envelope"></i>
                </button>
              </div>
            </div>

            <div className="row justify-content-center">
              <div className="col-12 col-md-10 col-lg-8">
                <div className="card border-0 shadow-sm">
                  <div className="card-body p-3 p-md-4">
                    <h5 className="card-title mb-2 mb-md-3 h6 h5-md">Invite a Friend</h5>
                    <form onSubmit={handleEmailShare}>
                      <div className="input-group mb-2 mb-md-3">
                        <input
                          type="email"
                          className="form-control"
                          placeholder="Friend's email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                        />
                        <button className="btn btn-primary" type="submit">
                          <i className="bi bi-send d-md-none"></i>
                          <span className="d-none d-md-inline"><i className="bi bi-send me-2"></i>Send</span>
                        </button>
                      </div>
                      <small className="text-muted d-block small">
                        We'll send them an invitation to join our food recipe community.
                      </small>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SocialShare;
