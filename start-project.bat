@echo off
echo ========================================
echo    Food Recipe Application Setup
echo ========================================
echo.

echo [1/6] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✓ Node.js is installed

echo.
echo [2/6] Installing server dependencies...
cd server
if not exist node_modules (
    echo Installing server packages...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install server dependencies
        pause
        exit /b 1
    )
) else (
    echo ✓ Server dependencies already installed
)

echo.
echo [3/6] Installing client dependencies...
cd ..\client
if not exist node_modules (
    echo Installing client packages...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install client dependencies
        pause
        exit /b 1
    )
) else (
    echo ✓ Client dependencies already installed
)

echo.
echo [4/6] Testing MongoDB connection...
cd ..\server
node -e "
const mongoose = require('mongoose');
const uri = 'mongodb+srv://ziqra5177:<EMAIL>/food-recipe-app?retryWrites=true&w=majority&appName=Cluster0';
mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true, serverSelectionTimeoutMS: 5000 })
  .then(() => { console.log('✓ MongoDB Atlas connection successful'); process.exit(0); })
  .catch(() => { console.log('⚠ MongoDB Atlas connection failed - will use mock data'); process.exit(0); });
"

echo.
echo [5/6] Starting the application...
echo Starting backend server...
start "Food Recipe Backend" cmd /k "cd /d %cd% && npm run dev"

echo Waiting for backend to start...
timeout /t 3 /nobreak >nul

echo Starting frontend server...
cd ..\client
start "Food Recipe Frontend" cmd /k "cd /d %cd% && npm run dev"

echo.
echo [6/6] Opening application in browser...
timeout /t 5 /nobreak >nul
start http://localhost:5173

echo.
echo ========================================
echo    Application Started Successfully!
echo ========================================
echo.
echo Frontend: http://localhost:5173
echo Backend:  http://localhost:5001
echo.
echo Press any key to close this window...
pause >nul
