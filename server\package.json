{"name": "food-recipe-server", "version": "1.0.0", "description": "Backend for Food Recipe App - A comprehensive recipe management system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"No tests specified\" && exit 0", "seed": "node scripts/seedData.js"}, "keywords": ["food", "recipe", "cooking", "api", "nodejs", "express", "mongodb"], "author": "Food Recipe Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "morgan": "^1.10.0", "validator": "^13.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}