import { useState, useEffect } from 'react'
import { getRecipes } from './services/api'

function SimpleApp() {
  const [recipes, setRecipes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchRecipes = async () => {
      try {
        const data = await getRecipes();
        setRecipes(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching recipes:', err);
        setError('Failed to fetch recipes');
        setLoading(false);
      }
    };

    fetchRecipes();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div>
      <h1>Food Recipes</h1>
      <div>
        {recipes.map((recipe) => (
          <div key={recipe._id} style={{ margin: '20px', padding: '10px', border: '1px solid #ccc' }}>
            <h2>{recipe.title}</h2>
            <p>{recipe.description}</p>
            <div>
              <strong>Cooking Time:</strong> {recipe.cookingTime} minutes
            </div>
            <div>
              <strong>Servings:</strong> {recipe.servings}
            </div>
            <div>
              <strong>Difficulty:</strong> {recipe.difficulty}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default SimpleApp
