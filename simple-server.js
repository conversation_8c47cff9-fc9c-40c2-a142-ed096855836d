const http = require('http');

// Sample data
const recipes = [
  {
    _id: '1',
    title: 'Spaghetti Carbonara',
    description: 'A classic Italian pasta dish with eggs, cheese, pancetta, and black pepper.',
    ingredients: ['200g spaghetti', '100g pancetta', '2 large eggs', '50g Pecorino Romano cheese', 'Black pepper'],
    instructions: 'Cook pasta. Fry pancetta. Mix eggs and cheese. Combine all ingredients.',
    cookingTime: 20,
    servings: 2,
    difficulty: 'Medium',
    imageUrl: 'https://images.unsplash.com/photo-1612874742237-6526221588e3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1771&q=80',
    createdAt: new Date()
  },
  {
    _id: '2',
    title: 'Chicken Curry',
    description: 'A flavorful Indian curry with tender chicken pieces in a rich sauce.',
    ingredients: ['500g chicken', '1 onion', '2 cloves garlic', '1 tbsp curry powder', 'Coconut milk'],
    instructions: 'Sauté onions and garlic. Add chicken and spices. Pour in coconut milk and simmer.',
    cookingTime: 40,
    servings: 4,
    difficulty: 'Medium',
    imageUrl: 'https://images.unsplash.com/photo-1604952564555-13c872c0266f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80',
    createdAt: new Date()
  }
];

console.log('Starting server...');

// Create server
const server = http.createServer((req, res) => {
  console.log(`Received request: ${req.method} ${req.url}`);
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(204);
    res.end();
    return;
  }

  // Set content type
  res.setHeader('Content-Type', 'application/json');

  // Handle API routes
  if (req.url === '/api/recipes' && req.method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify(recipes));
  } else if (req.url.match(/\/api\/recipes\/([0-9]+)/) && req.method === 'GET') {
    const id = req.url.split('/')[3];
    const recipe = recipes.find(r => r._id === id);

    if (recipe) {
      res.writeHead(200);
      res.end(JSON.stringify(recipe));
    } else {
      res.writeHead(404);
      res.end(JSON.stringify({ message: 'Recipe not found' }));
    }
  } else {
    res.writeHead(404);
    res.end(JSON.stringify({ message: 'Route not found' }));
  }
});

// Start server
const PORT = 3002;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log('Server is ready to accept connections');
});
