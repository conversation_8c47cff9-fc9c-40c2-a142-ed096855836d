<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple Food Recipe App</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .recipe {
      border: 1px solid #ddd;
      padding: 15px;
      margin-bottom: 20px;
      border-radius: 5px;
    }
    .recipe h2 {
      margin-top: 0;
    }
    .loading, .error {
      text-align: center;
      padding: 20px;
    }
  </style>
</head>
<body>
  <h1>Food Recipes</h1>
  <div id="recipes-container">
    <div class="loading">Loading recipes...</div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const recipesContainer = document.getElementById('recipes-container');

      // Fetch recipes from API
      fetch('http://localhost:3002/api/recipes')
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.json();
        })
        .then(recipes => {
          // Clear loading message
          recipesContainer.innerHTML = '';

          // Display recipes
          recipes.forEach(recipe => {
            const recipeElement = document.createElement('div');
            recipeElement.className = 'recipe';

            recipeElement.innerHTML = `
              <h2>${recipe.title}</h2>
              <p>${recipe.description}</p>
              <div><strong>Cooking Time:</strong> ${recipe.cookingTime} minutes</div>
              <div><strong>Servings:</strong> ${recipe.servings}</div>
              <div><strong>Difficulty:</strong> ${recipe.difficulty}</div>
              <h3>Ingredients:</h3>
              <ul>
                ${recipe.ingredients.map(ingredient => `<li>${ingredient}</li>`).join('')}
              </ul>
              <h3>Instructions:</h3>
              <p>${recipe.instructions}</p>
            `;

            recipesContainer.appendChild(recipeElement);
          });
        })
        .catch(error => {
          recipesContainer.innerHTML = `<div class="error">Error: ${error.message}</div>`;
          console.error('Error fetching recipes:', error);
        });
    });
  </script>
</body>
</html>
