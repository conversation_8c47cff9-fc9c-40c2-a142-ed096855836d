{"name": "food-recipe-app", "version": "1.0.0", "description": "MERN Stack Food Recipe Web App", "main": "index.js", "scripts": {"start": "node server/server.js", "server": "nodemon server/server.js", "client": "cd client && npm run dev", "dev": "concurrently \"npm run server\" \"npm run client\"", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm install && npm run install-server && npm run install-client"}, "keywords": ["mern", "food", "recipe", "mongodb", "express", "react", "node"], "author": "", "license": "MIT", "dependencies": {"concurrently": "^8.2.1"}, "devDependencies": {"nodemon": "^3.0.1"}}