import { useState, useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import Navbar from './components/Navbar'
import Home from './components/home'
import RecipeList from './components/RecipeList'
import RecipeDetail from './components/RecipeDetail'
import RecipeForm from './components/RecipeForm'
import Login from './components/auth/Login'
import Register from './components/auth/Register'
import Profile from './components/auth/Profile'
import Favorites from './components/auth/Favorites'
import ProtectedRoute from './components/auth/ProtectedRoute'
import ConnectionTest from './components/ConnectionTest'
import './App.css'

function App() {
  return (
    <>
      <Navbar />
      <div className="container mt-4">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/recipes" element={<RecipeList />} />
          <Route path="/recipes/:id" element={<RecipeDetail />} />
          <Route path="/add-recipe" element={
            <ProtectedRoute>
              <RecipeForm />
            </ProtectedRoute>
          } />
          <Route path="/edit-recipe/:id" element={
            <ProtectedRoute>
              <RecipeForm />
            </ProtectedRoute>
          } />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/profile" element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          } />
          <Route path="/favorites" element={
            <ProtectedRoute>
              <Favorites />
            </ProtectedRoute>
          } />
          <Route path="/test-connection" element={<ConnectionTest />} />
        </Routes>
      </div>
    </>
  )
}

export default App
