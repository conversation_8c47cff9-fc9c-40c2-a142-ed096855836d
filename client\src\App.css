/* Global Styles */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
}

/* Header Styles */
.app-header {
  background-color: #f8f9fa;
  padding: 2rem 0;
  margin-bottom: 2rem;
}

/* Recipe Form Styles */
.recipe-form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

/* Recipe Detail Styles */
.recipe-detail-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.recipe-image-container {
  max-height: 400px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.recipe-image-container img {
  width: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.recipe-image-container:hover img {
  transform: scale(1.03);
}

.cooking-info {
  display: flex;
  justify-content: space-between;
  margin: 1.5rem 0;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.cooking-info-item {
  text-align: center;
}

.cooking-info-item p {
  margin: 0;
  font-weight: bold;
}

.cooking-info-item span {
  font-size: 0.9rem;
  color: #6c757d;
}

/* Home Page Styles */
.home-page {
  overflow-x: hidden;
}

.hero-section {
  position: relative;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-section h1 {
  font-weight: 700;
}

.card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: none;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-img-top {
  transition: transform 0.5s ease;
}

.card:hover .card-img-top {
  transform: scale(1.05);
}

/* Testimonials */
.carousel-item {
  transition: transform 0.6s ease-in-out;
}

/* Newsletter */
.bg-light {
  background-color: #f8f9fa;
}

/* Recipe of the Day Ribbon */
.ribbon {
  width: 150px;
  height: 150px;
  overflow: hidden;
  position: absolute;
  z-index: 1;
}

.ribbon span {
  position: absolute;
  display: block;
  width: 225px;
  padding: 8px 0;
  background-color: #ff6b6b;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  text-align: center;
}

.ribbon-top-right {
  top: -10px;
  right: -10px;
}

.ribbon-top-right span {
  left: -25px;
  top: 30px;
  transform: rotate(45deg);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.featured-recipes, .categories, .testimonials, .newsletter, .recipe-of-the-day, .cooking-tips {
  animation: fadeIn 0.8s ease-out forwards;
}

/* Staggered animation for cooking tips */
.tip-card {
  opacity: 0;
  animation: fadeIn 0.5s ease-out forwards;
}

.tip-card:nth-child(1) { animation-delay: 0.1s; }
.tip-card:nth-child(2) { animation-delay: 0.3s; }
.tip-card:nth-child(3) { animation-delay: 0.5s; }

/* Responsive Typography */
@media (max-width: 767.98px) {
  h1, .h1 { font-size: 1.75rem; }
  h2, .h2 { font-size: 1.5rem; }
  h3, .h3 { font-size: 1.25rem; }
  h4, .h4 { font-size: 1.15rem; }
  h5, .h5 { font-size: 1rem; }
  .display-4 { font-size: 1.75rem; }
  .lead { font-size: 1rem; }
  .testimonial-text { font-size: 0.9rem; }
}

/* Responsive Spacing */
@media (max-width: 767.98px) {
  .container { padding-left: 15px; padding-right: 15px; }
}

/* Responsive Utility Classes */
.h5-md { font-size: 1rem; }
.h6 { font-size: 0.9rem; }

@media (min-width: 768px) {
  .h5-md { font-size: 1.25rem; }
  .fs-md-4 { font-size: 1.5rem !important; }
  .w-md-auto { width: auto !important; }
  .w-md-75 { width: 75% !important; }
  .px-md-3 { padding-left: 1rem !important; padding-right: 1rem !important; }
  .rounded-md-0 { border-radius: 0 !important; }
  .rounded-md-top {
    border-top-left-radius: 0.375rem !important;
    border-top-right-radius: 0.375rem !important;
  }
}
