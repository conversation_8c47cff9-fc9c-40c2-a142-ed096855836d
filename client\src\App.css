/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #2d3748;
  background-color: #ffffff;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 0.5rem;
  color: #1a202c;
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
  font-family: 'Playfair Display', serif;
  font-weight: 800;
}

p, .lead {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
}

/* Custom CSS Variables */
:root {
  --primary-color: #667eea;
  --primary-dark: #5a6fd8;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --accent-secondary: #f5576c;
  --success-color: #48bb78;
  --danger-color: #f56565;
  --warning-color: #ed8936;
  --info-color: #4299e1;
  --light-color: #f7fafc;
  --dark-color: #1a202c;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  --gradient-warm: linear-gradient(135deg, #fed7aa 0%, #fbbf24 100%);
  --gradient-cool: linear-gradient(135deg, #a7f3d0 0%, #34d399 100%);
  --gradient-sunset: linear-gradient(135deg, #fecaca 0%, #f87171 100%);
  --gradient-ocean: linear-gradient(135deg, #bfdbfe 0%, #3b82f6 100%);
  --gradient-light: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  /* Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-3xl: 2rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

/* Header Styles */
.app-header {
  background-color: #f8f9fa;
  padding: 2rem 0;
  margin-bottom: 2rem;
}

/* Recipe Form Styles */
.recipe-form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

/* Recipe Detail Styles */
.recipe-detail-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.recipe-image-container {
  max-height: 400px;
  overflow: hidden;
  border-radius: var(--radius-xl);
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
}

.recipe-image-container img {
  width: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.recipe-image-container:hover img {
  transform: scale(1.03);
}

.cooking-info {
  display: flex;
  justify-content: space-between;
  margin: 1.5rem 0;
  padding: 1rem;
  background-color: var(--gray-50);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.cooking-info-item {
  text-align: center;
}

.cooking-info-item p {
  margin: 0;
  font-weight: bold;
}

.cooking-info-item span {
  font-size: 0.9rem;
  color: var(--gray-500);
}

/* Utility Classes */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.text-gradient-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-light {
  background: var(--gradient-light);
}

.bg-primary-soft {
  background-color: rgba(102, 126, 234, 0.1);
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.text-white-75 {
  color: rgba(255, 255, 255, 0.75);
}

.shadow-hover {
  transition: all var(--transition-normal);
}

.shadow-hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Home Page Styles */
.home-page {
  overflow-x: hidden;
}

/* Hero Section Styles */
.hero-section {
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0.9;
  z-index: 1;
}

.hero-section .container {
  position: relative;
  z-index: 2;
}

.hero-section h1 {
  font-weight: 700;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: clamp(1.1rem, 2.5vw, 1.5rem);
  font-weight: 400;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  opacity: 0.95;
}

/* Particles Background */
.particles-bg {
  pointer-events: none;
  z-index: 0;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle-2 {
  width: 60px;
  height: 60px;
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.particle-3 {
  width: 40px;
  height: 40px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

.particle-4 {
  width: 100px;
  height: 100px;
  top: 40%;
  left: 70%;
  animation-delay: 1s;
}

.particle-5 {
  width: 50px;
  height: 50px;
  top: 10%;
  left: 60%;
  animation-delay: 3s;
}

/* Enhanced Search Bar */
.search-container {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-form {
  position: relative;
}

.search-input-group {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--radius-full);
  padding: 8px;
  box-shadow: var(--shadow-2xl);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all var(--transition-normal);
}

.search-input-group:focus-within {
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), var(--shadow-2xl);
  transform: translateY(-2px);
}

.search-icon {
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-500);
  z-index: 3;
  font-size: 1.1rem;
}

.search-input {
  border: none;
  background: transparent;
  padding: 16px 24px 16px 56px;
  font-size: 1.1rem;
  border-radius: var(--radius-full);
  flex: 1;
  color: var(--gray-700);
  font-weight: 500;
}

.search-input::placeholder {
  color: var(--gray-400);
  font-weight: 400;
}

.search-input:focus {
  outline: none;
  box-shadow: none;
}

.search-btn {
  border-radius: var(--radius-full);
  padding: 16px 32px;
  border: none;
  background: var(--gradient-primary);
  color: white;
  font-weight: 600;
  font-size: 1rem;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.search-btn:hover {
  transform: translateX(2px);
  box-shadow: var(--shadow-lg);
  background: var(--gradient-primary);
  filter: brightness(1.1);
}

.popular-searches {
  text-align: center;
  margin-top: 1rem;
}

.popular-tag {
  display: inline-block;
  padding: 6px 16px;
  margin: 0 8px 8px 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 0.9rem;
  font-weight: 500;
}

.popular-tag:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
}

/* Scroll Indicator */
.scroll-indicator {
  animation: bounce 2s infinite;
  opacity: 0.8;
}

.scroll-arrow {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.5rem;
}

/* Categories Section */
.categories-section {
  background: var(--gradient-light);
  position: relative;
}

.category-link {
  display: block;
  transition: all var(--transition-normal);
}

.category-card {
  transition: all var(--transition-normal);
  cursor: pointer;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  position: relative;
}

.category-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-2xl);
}

.category-card:hover .category-image {
  transform: scale(1.1);
}

.category-card:hover .category-hover-effect {
  opacity: 1;
}

.category-image {
  transition: transform var(--transition-slow);
  border-radius: var(--radius-2xl);
}

.category-overlay {
  background: linear-gradient(45deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, transparent 100%);
  border-radius: var(--radius-2xl);
}

.category-content {
  position: relative;
  z-index: 2;
}

.category-icon {
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.category-card:hover .category-icon {
  transform: scale(1.1) rotate(5deg);
}

.category-hover-effect {
  transition: all var(--transition-normal);
  backdrop-filter: blur(5px);
  border-radius: var(--radius-2xl);
}

.category-stats .badge {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Featured Recipes Section */
.featured-recipes-section {
  position: relative;
  background: var(--gray-50);
}

.recipe-card {
  transition: all var(--transition-normal);
  cursor: pointer;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  border: 1px solid var(--gray-200);
  background: white;
}

.recipe-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-color);
}

.recipe-card:hover .recipe-image {
  transform: scale(1.08);
}

.recipe-card:hover .recipe-overlay {
  opacity: 1;
}

.recipe-card:hover .recipe-title {
  color: var(--primary-color);
}

.recipe-image-container {
  overflow: hidden;
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
  position: relative;
}

.recipe-image {
  transition: transform var(--transition-slow);
  width: 100%;
  height: 250px;
  object-fit: cover;
}

.recipe-overlay {
  transition: all var(--transition-normal);
  backdrop-filter: blur(5px);
  opacity: 0;
}

.recipe-title {
  transition: color var(--transition-normal);
  font-size: 1.25rem;
  font-weight: 700;
}

.recipe-description {
  line-height: 1.6;
  color: var(--gray-600);
}

.recipe-meta {
  font-size: 0.875rem;
  color: var(--gray-500);
}

.author-avatar {
  transition: all var(--transition-normal);
  background: var(--gradient-primary);
}

.recipe-card:hover .author-avatar {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

/* Enhanced Cards */
.card {
  border: none;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.card-img-top {
  transition: transform var(--transition-slow);
}

.card:hover .card-img-top {
  transform: scale(1.05);
}

/* Enhanced Buttons */
.btn {
  border-radius: var(--radius-full);
  font-weight: 600;
  transition: all var(--transition-normal);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-2px);
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: var(--gradient-primary);
  filter: brightness(1.1);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-outline-primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--gradient-primary);
  border-color: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-lg);
}

.btn-lg {
  padding: 12px 32px;
  font-size: 1.1rem;
}

/* Enhanced Badges */
.badge {
  font-weight: 500;
  padding: 8px 16px;
  border-radius: var(--radius-full);
  font-size: 0.875rem;
}

.bg-success {
  background: var(--gradient-success) !important;
}

.bg-warning {
  background: var(--gradient-warm) !important;
  color: var(--gray-800) !important;
}

.bg-danger {
  background: var(--gradient-sunset) !important;
}

/* Loading States */
.placeholder {
  border-radius: var(--radius-md);
  background: var(--gray-200);
}

.placeholder-glow .placeholder {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(90deg, var(--gray-200) 0px, var(--gray-300) 40px, var(--gray-200) 80px);
  background-size: 200px 100%;
}

/* Empty States */
.empty-state {
  padding: 3rem 1rem;
}

.empty-state i {
  opacity: 0.5;
}

/* Testimonials */
.carousel-item {
  transition: transform 0.6s ease-in-out;
}

/* Newsletter */
.bg-light {
  background-color: var(--gray-50);
}

/* Recipe of the Day Ribbon */
.ribbon {
  width: 150px;
  height: 150px;
  overflow: hidden;
  position: absolute;
  z-index: 1;
}

.ribbon span {
  position: absolute;
  display: block;
  width: 225px;
  padding: 8px 0;
  background-color: #ff6b6b;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  text-align: center;
}

.ribbon-top-right {
  top: -10px;
  right: -10px;
}

.ribbon-top-right span {
  left: -25px;
  top: 30px;
  transform: rotate(45deg);
}

/* Staggered animation for cooking tips */
.tip-card {
  opacity: 0;
  animation: fadeIn 0.5s ease-out forwards;
}

.tip-card:nth-child(1) { animation-delay: 0.1s; }
.tip-card:nth-child(2) { animation-delay: 0.3s; }
.tip-card:nth-child(3) { animation-delay: 0.5s; }

.featured-recipes, .categories, .testimonials, .newsletter, .recipe-of-the-day, .cooking-tips {
  animation: fadeIn 0.8s ease-out forwards;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-title {
    font-size: 3.5rem;
  }
}

@media (max-width: 992px) {
  .hero-title {
    font-size: 3rem;
  }

  .category-content {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 80vh;
    background-attachment: scroll;
  }

  .hero-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .search-input-group {
    padding: 6px;
  }

  .search-input {
    padding: 12px 16px 12px 48px;
    font-size: 1rem;
  }

  .search-btn {
    padding: 12px 24px;
    font-size: 0.9rem;
  }

  .category-content {
    padding: 1rem;
  }

  .recipe-card .card-body {
    padding: 1.5rem;
  }

  .popular-tag {
    margin: 0 4px 8px 0;
    padding: 4px 12px;
    font-size: 0.8rem;
  }
}

/* Responsive Typography */
@media (max-width: 767.98px) {
  h1, .h1 { font-size: 1.75rem; }
  h2, .h2 { font-size: 1.5rem; }
  h3, .h3 { font-size: 1.25rem; }
  h4, .h4 { font-size: 1.15rem; }
  h5, .h5 { font-size: 1rem; }
  .display-4 { font-size: 1.75rem; }
  .lead { font-size: 1rem; }
  .testimonial-text { font-size: 0.9rem; }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .search-input {
    padding: 10px 12px 10px 40px;
    font-size: 0.9rem;
  }

  .search-btn {
    padding: 10px 20px;
  }

  .search-icon {
    left: 16px;
    font-size: 1rem;
  }

  .category-content {
    padding: 0.75rem;
  }

  .recipe-card .card-body {
    padding: 1rem;
  }

  .btn-lg {
    padding: 10px 24px;
    font-size: 1rem;
  }
}

/* Responsive Spacing */
@media (max-width: 767.98px) {
  .container { padding-left: 15px; padding-right: 15px; }
}

/* Responsive Utility Classes */
.h5-md { font-size: 1rem; }
.h6 { font-size: 0.9rem; }

@media (min-width: 768px) {
  .h5-md { font-size: 1.25rem; }
  .fs-md-4 { font-size: 1.5rem !important; }
  .w-md-auto { width: auto !important; }
  .w-md-75 { width: 75% !important; }
  .px-md-3 { padding-left: 1rem !important; padding-right: 1rem !important; }
  .rounded-md-0 { border-radius: 0 !important; }
  .rounded-md-top {
    border-top-left-radius: 0.375rem !important;
    border-top-right-radius: 0.375rem !important;
  }
}

/* Focus States */
.btn:focus,
.form-control:focus {
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Selection */
::selection {
  background: var(--primary-color);
  color: white;
}

::-moz-selection {
  background: var(--primary-color);
  color: white;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* Print Styles */
@media print {
  .hero-section,
  .categories-section,
  .featured-recipes-section {
    background: white !important;
    color: black !important;
  }

  .btn,
  .badge {
    border: 1px solid #000 !important;
  }

  .shadow-hover,
  .recipe-card,
  .category-card {
    box-shadow: none !important;
  }
}
